{"date": "2025-06-30", "crawl_time": "2025-06-30T13:42:52.689452", "total_count": 16, "repositories": [{"name": "twentyhq /\n\n      twenty", "url": "https://github.com/twentyhq/twenty", "description": "Building a modern alternative to Salesforce, powered by the community.", "language": "TypeScript", "stars": 31624, "forks": 3549, "today_stars": 1002, "authors": [], "crawl_time": "2025-06-30T13:42:52.683448"}, {"name": "GraphiteEditor /\n\n      Graphite", "url": "https://github.com/GraphiteEditor/Graphite", "description": "2D vector & raster editor that melds traditional layers & tools with a modern node-based, non-destructive, procedural workflow.", "language": "Rust", "stars": 14731, "forks": 674, "today_stars": 374, "authors": [], "crawl_time": "2025-06-30T13:42:52.684569"}, {"name": "octra-labs /\n\n      wallet-gen", "url": "https://github.com/octra-labs/wallet-gen", "description": "暂无描述", "language": "HTML", "stars": 320, "forks": 6285, "today_stars": 68, "authors": [], "crawl_time": "2025-06-30T13:42:52.684771"}, {"name": "microsoft /\n\n      generative-ai-for-beginners", "url": "https://github.com/microsoft/generative-ai-for-beginners", "description": "21 Lessons, Get Started Building with Generative AI 🔗 https://microsoft.github.io/generative-ai-for-beginners/", "language": "Jupyter Notebook", "stars": 86766, "forks": 45585, "today_stars": 296, "authors": [], "crawl_time": "2025-06-30T13:42:52.685117"}, {"name": "x1xhlol /\n\n      system-prompts-and-models-of-ai-tools", "url": "https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools", "description": "FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, Replit Agent, Windsurf Agent, VSCode Agent, <PERSON>a Browser, Trae AI & Cluely (And other Open Sourced) System Prompts, Tools & AI Models.", "language": "未知", "stars": 63178, "forks": 18548, "today_stars": 866, "authors": [], "crawl_time": "2025-06-30T13:42:52.685426"}, {"name": "coleam00 /\n\n      ottomator-agents", "url": "https://github.com/coleam00/ottomator-agents", "description": "All the open source AI Agents hosted on the oTTomator Live Agent Studio platform!", "language": "Python", "stars": 3102, "forks": 1154, "today_stars": 164, "authors": [], "crawl_time": "2025-06-30T13:42:52.685664"}, {"name": "stanford-oval /\n\n      storm", "url": "https://github.com/stanford-oval/storm", "description": "An LLM-powered knowledge curation system that researches a topic and generates a full-length report with citations.", "language": "Python", "stars": 25295, "forks": 2293, "today_stars": 154, "authors": [], "crawl_time": "2025-06-30T13:42:52.685922"}, {"name": "jnsahaj /\n\n      tweakcn", "url": "https://github.com/jnsahaj/tweakcn", "description": "A visual no-code theme editor for shadcn/ui components", "language": "TypeScript", "stars": 4619, "forks": 237, "today_stars": 126, "authors": ["lovable-dev"], "crawl_time": "2025-06-30T13:42:52.686222"}, {"name": "mendableai /\n\n      firecrawl", "url": "https://github.com/mendableai/firecrawl", "description": "🔥 Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.", "language": "TypeScript", "stars": 41127, "forks": 3878, "today_stars": 176, "authors": [], "crawl_time": "2025-06-30T13:42:52.686440"}, {"name": "ItzCrazyKns /\n\n      Perplexica", "url": "https://github.com/ItzCrazyKns/Perplexica", "description": "Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI", "language": "TypeScript", "stars": 22802, "forks": 2364, "today_stars": 26, "authors": [], "crawl_time": "2025-06-30T13:42:52.686651"}, {"name": "adityachandelgit /\n\n      BookLore", "url": "https://github.com/adityachandelgit/BookLore", "description": "BookLore is a web app for hosting, managing, and exploring books, with support for PDFs, eBooks, reading progress, metadata, and stats.", "language": "Java", "stars": 2164, "forks": 88, "today_stars": 299, "authors": [], "crawl_time": "2025-06-30T13:42:52.687024"}, {"name": "swisskyrepo /\n\n      PayloadsAllTheThings", "url": "https://github.com/swisskyrepo/PayloadsAllTheThings", "description": "A list of useful payloads and bypass for Web Application Security and Pentest/CTF", "language": "Python", "stars": 66569, "forks": 15511, "today_stars": 154, "authors": [], "crawl_time": "2025-06-30T13:42:52.687274"}, {"name": "mikumifa /\n\n      biliTickerBuy", "url": "https://github.com/mikumifa/biliTickerBuy", "description": "b站会员购购票辅助工具", "language": "Python", "stars": 2510, "forks": 335, "today_stars": 63, "authors": ["github-actions"], "crawl_time": "2025-06-30T13:42:52.687478"}, {"name": "m1k1o /\n\n      neko", "url": "https://github.com/m1k1o/neko", "description": "A self hosted virtual browser that runs in docker and uses WebRTC.", "language": "Go", "stars": 11382, "forks": 759, "today_stars": 297, "authors": [], "crawl_time": "2025-06-30T13:42:52.687731"}, {"name": "LMCache /\n\n      LMCache", "url": "https://github.com/LMCache/LMCache", "description": "Supercharge Your LLM with the Fastest KV Cache Layer", "language": "Python", "stars": 2229, "forks": 272, "today_stars": 276, "authors": [], "crawl_time": "2025-06-30T13:42:52.687934"}, {"name": "zyronon /\n\n      typing-word", "url": "https://github.com/zyronon/typing-word", "description": "在网页上背单词", "language": "<PERSON><PERSON>", "stars": 1962, "forks": 234, "today_stars": 110, "authors": [], "crawl_time": "2025-06-30T13:42:52.688128"}]}