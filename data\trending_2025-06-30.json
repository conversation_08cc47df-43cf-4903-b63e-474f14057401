{"date": "2025-06-30", "crawl_time": "2025-06-30T15:35:13.265978", "total_count": 16, "repositories": [{"name": "twentyhq /   twenty", "url": "https://github.com/twentyhq/twenty", "description": "Building a modern alternative to Salesforce, powered by the community.", "language": "TypeScript", "stars": 31756, "forks": 3554, "today_stars": 1002, "authors": [], "crawl_time": "2025-06-30T15:35:13.261899"}, {"name": "GraphiteEditor /   Graphite", "url": "https://github.com/GraphiteEditor/Graphite", "description": "2D vector & raster editor that melds traditional layers & tools with a modern node-based, non-destructive, procedural workflow.", "language": "Rust", "stars": 14831, "forks": 676, "today_stars": 374, "authors": [], "crawl_time": "2025-06-30T15:35:13.262118"}, {"name": "octra-labs /   wallet-gen", "url": "https://github.com/octra-labs/wallet-gen", "description": "暂无描述", "language": "HTML", "stars": 335, "forks": 6465, "today_stars": 68, "authors": [], "crawl_time": "2025-06-30T15:35:13.262279"}, {"name": "microsoft /   generative-ai-for-beginners", "url": "https://github.com/microsoft/generative-ai-for-beginners", "description": "21 Lessons, Get Started Building with Generative AI 🔗 https://microsoft.github.io/generative-ai-for-beginners/", "language": "Jupyter Notebook", "stars": 86810, "forks": 45604, "today_stars": 296, "authors": [], "crawl_time": "2025-06-30T15:35:13.262460"}, {"name": "x1xhlol /   system-prompts-and-models-of-ai-tools", "url": "https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools", "description": "FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, Replit Agent, Windsurf Agent, VSCode Agent, <PERSON>a Browser, Trae AI & Cluely (And other Open Sourced) System Prompts, Tools & AI Models.", "language": "未知", "stars": 63255, "forks": 18558, "today_stars": 866, "authors": [], "crawl_time": "2025-06-30T15:35:13.262616"}, {"name": "coleam00 /   ottomator-agents", "url": "https://github.com/coleam00/ottomator-agents", "description": "All the open source AI Agents hosted on the oTTomator Live Agent Studio platform!", "language": "Python", "stars": 3110, "forks": 1154, "today_stars": 164, "authors": [], "crawl_time": "2025-06-30T15:35:13.262777"}, {"name": "stanford-oval /   storm", "url": "https://github.com/stanford-oval/storm", "description": "An LLM-powered knowledge curation system that researches a topic and generates a full-length report with citations.", "language": "Python", "stars": 25342, "forks": 2297, "today_stars": 154, "authors": [], "crawl_time": "2025-06-30T15:35:13.262941"}, {"name": "jnsahaj /   tweakcn", "url": "https://github.com/jnsahaj/tweakcn", "description": "A visual no-code theme editor for shadcn/ui components", "language": "TypeScript", "stars": 4660, "forks": 239, "today_stars": 126, "authors": ["lovable-dev"], "crawl_time": "2025-06-30T15:35:13.263130"}, {"name": "mendableai /   firecrawl", "url": "https://github.com/mendableai/firecrawl", "description": "🔥 Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.", "language": "TypeScript", "stars": 41154, "forks": 3880, "today_stars": 176, "authors": [], "crawl_time": "2025-06-30T15:35:13.263289"}, {"name": "ItzCrazyKns /   Perplexica", "url": "https://github.com/ItzCrazyKns/Perplexica", "description": "Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI", "language": "TypeScript", "stars": 22825, "forks": 2364, "today_stars": 26, "authors": [], "crawl_time": "2025-06-30T15:35:13.263445"}, {"name": "adityachandelgit /   BookLore", "url": "https://github.com/adityachandelgit/BookLore", "description": "BookLore is a web app for hosting, managing, and exploring books, with support for PDFs, eBooks, reading progress, metadata, and stats.", "language": "Java", "stars": 2199, "forks": 90, "today_stars": 299, "authors": [], "crawl_time": "2025-06-30T15:35:13.263608"}, {"name": "swisskyrepo /   PayloadsAllTheThings", "url": "https://github.com/swisskyrepo/PayloadsAllTheThings", "description": "A list of useful payloads and bypass for Web Application Security and Pentest/CTF", "language": "Python", "stars": 66591, "forks": 15514, "today_stars": 154, "authors": [], "crawl_time": "2025-06-30T15:35:13.263785"}, {"name": "mikumifa /   biliTickerBuy", "url": "https://github.com/mikumifa/biliTickerBuy", "description": "b站会员购购票辅助工具", "language": "Python", "stars": 2522, "forks": 335, "today_stars": 63, "authors": ["github-actions"], "crawl_time": "2025-06-30T15:35:13.263948"}, {"name": "m1k1o /   neko", "url": "https://github.com/m1k1o/neko", "description": "A self hosted virtual browser that runs in docker and uses WebRTC.", "language": "Go", "stars": 11412, "forks": 759, "today_stars": 297, "authors": [], "crawl_time": "2025-06-30T15:35:13.264131"}, {"name": "LMCache /   LMCache", "url": "https://github.com/LMCache/LMCache", "description": "Supercharge Your LLM with the Fastest KV Cache Layer", "language": "Python", "stars": 2254, "forks": 273, "today_stars": 276, "authors": [], "crawl_time": "2025-06-30T15:35:13.264313"}, {"name": "zyronon /   typing-word", "url": "https://github.com/zyronon/typing-word", "description": "在网页上背单词", "language": "<PERSON><PERSON>", "stars": 1989, "forks": 236, "today_stars": 110, "authors": [], "crawl_time": "2025-06-30T15:35:13.264489"}]}