{"date": "2025-06-30", "crawl_time": "2025-06-30T16:11:02.870747", "total_count": 16, "repositories": [{"name": "twentyhq /   twenty", "url": "https://github.com/twentyhq/twenty", "description": "Building a modern alternative to Salesforce, powered by the community.", "language": "TypeScript", "stars": 31810, "forks": 3557, "today_stars": 1002, "authors": [], "crawl_time": "2025-06-30T16:11:02.866469"}, {"name": "GraphiteEditor /   Graphite", "url": "https://github.com/GraphiteEditor/Graphite", "description": "2D vector & raster editor that melds traditional layers & tools with a modern node-based, non-destructive, procedural workflow.", "language": "Rust", "stars": 14869, "forks": 676, "today_stars": 374, "authors": [], "crawl_time": "2025-06-30T16:11:02.866706"}, {"name": "octra-labs /   wallet-gen", "url": "https://github.com/octra-labs/wallet-gen", "description": "暂无描述", "language": "HTML", "stars": 342, "forks": 6525, "today_stars": 68, "authors": [], "crawl_time": "2025-06-30T16:11:02.866885"}, {"name": "microsoft /   generative-ai-for-beginners", "url": "https://github.com/microsoft/generative-ai-for-beginners", "description": "21 Lessons, Get Started Building with Generative AI 🔗 https://microsoft.github.io/generative-ai-for-beginners/", "language": "Jupyter Notebook", "stars": 86822, "forks": 45609, "today_stars": 296, "authors": [], "crawl_time": "2025-06-30T16:11:02.867093"}, {"name": "x1xhlol /   system-prompts-and-models-of-ai-tools", "url": "https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools", "description": "FULL v0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.dev, <PERSON><PERSON>, <PERSON>, Replit Agent, Windsurf Agent, VSCode Agent, <PERSON>a Browser, Trae AI & Cluely (And other Open Sourced) System Prompts, Tools & AI Models.", "language": "未知", "stars": 63278, "forks": 18563, "today_stars": 866, "authors": [], "crawl_time": "2025-06-30T16:11:02.867282"}, {"name": "coleam00 /   ottomator-agents", "url": "https://github.com/coleam00/ottomator-agents", "description": "All the open source AI Agents hosted on the oTTomator Live Agent Studio platform!", "language": "Python", "stars": 3116, "forks": 1155, "today_stars": 164, "authors": [], "crawl_time": "2025-06-30T16:11:02.867639"}, {"name": "stanford-oval /   storm", "url": "https://github.com/stanford-oval/storm", "description": "An LLM-powered knowledge curation system that researches a topic and generates a full-length report with citations.", "language": "Python", "stars": 25354, "forks": 2298, "today_stars": 154, "authors": [], "crawl_time": "2025-06-30T16:11:02.867887"}, {"name": "jnsahaj /   tweakcn", "url": "https://github.com/jnsahaj/tweakcn", "description": "A visual no-code theme editor for shadcn/ui components", "language": "TypeScript", "stars": 4674, "forks": 239, "today_stars": 126, "authors": ["lovable-dev"], "crawl_time": "2025-06-30T16:11:02.868105"}, {"name": "mendableai /   firecrawl", "url": "https://github.com/mendableai/firecrawl", "description": "🔥 Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.", "language": "TypeScript", "stars": 41164, "forks": 3880, "today_stars": 176, "authors": [], "crawl_time": "2025-06-30T16:11:02.868284"}, {"name": "ItzCrazyKns /   Perplexica", "url": "https://github.com/ItzCrazyKns/Perplexica", "description": "Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI", "language": "TypeScript", "stars": 22837, "forks": 2364, "today_stars": 26, "authors": [], "crawl_time": "2025-06-30T16:11:02.868457"}, {"name": "adityachandelgit /   BookLore", "url": "https://github.com/adityachandelgit/BookLore", "description": "BookLore is a web app for hosting, managing, and exploring books, with support for PDFs, eBooks, reading progress, metadata, and stats.", "language": "Java", "stars": 2212, "forks": 90, "today_stars": 299, "authors": [], "crawl_time": "2025-06-30T16:11:02.868632"}, {"name": "swisskyrepo /   PayloadsAllTheThings", "url": "https://github.com/swisskyrepo/PayloadsAllTheThings", "description": "A list of useful payloads and bypass for Web Application Security and Pentest/CTF", "language": "Python", "stars": 66598, "forks": 15514, "today_stars": 154, "authors": [], "crawl_time": "2025-06-30T16:11:02.868839"}, {"name": "mikumifa /   biliTickerBuy", "url": "https://github.com/mikumifa/biliTickerBuy", "description": "b站会员购购票辅助工具", "language": "Python", "stars": 2524, "forks": 336, "today_stars": 63, "authors": ["github-actions"], "crawl_time": "2025-06-30T16:11:02.869020"}, {"name": "m1k1o /   neko", "url": "https://github.com/m1k1o/neko", "description": "A self hosted virtual browser that runs in docker and uses WebRTC.", "language": "Go", "stars": 11428, "forks": 759, "today_stars": 297, "authors": [], "crawl_time": "2025-06-30T16:11:02.869226"}, {"name": "LMCache /   LMCache", "url": "https://github.com/LMCache/LMCache", "description": "Supercharge Your LLM with the Fastest KV Cache Layer", "language": "Python", "stars": 2263, "forks": 273, "today_stars": 276, "authors": [], "crawl_time": "2025-06-30T16:11:02.869407"}, {"name": "zyronon /   typing-word", "url": "https://github.com/zyronon/typing-word", "description": "在网页上背单词", "language": "<PERSON><PERSON>", "stars": 1999, "forks": 236, "today_stars": 110, "authors": [], "crawl_time": "2025-06-30T16:11:02.869587"}]}