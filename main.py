"""
主程序入口 - GitHub热门项目爬虫的核心控制器
功能：爬取GitHub热门项目、生成报告、发送邮件、定时任务调度
"""

import schedule
import time
import logging
import sys
import argparse
from datetime import datetime
from github_spider import GitHubSpider
from report_generator import MarkdownReportGenerator
from data_manager import DataManager
from email_sender import EmailSender
from config import *

# 设置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GitHubTrendingBot:
    """GitHub热门仓库爬虫机器人"""
    
    def __init__(self):
        self.spider = GitHubSpider()
        self.report_generator = MarkdownReportGenerator()
        self.data_manager = DataManager()
        self.email_sender = EmailSender()
    
    def run_daily_task(self):
        """执行每日任务"""
        try:
            logger.info("=" * 50)
            logger.info("开始执行每日GitHub热门仓库爬取任务")
            logger.info("=" * 50)
            
            # 1. 爬取数据
            logger.info("步骤 1: 爬取GitHub热门仓库数据")
            repositories = self.spider.crawl_trending_repositories('daily')
            
            if not repositories:
                logger.error("未能获取到任何仓库数据，任务终止")
                return False
            
            logger.info(f"成功爬取 {len(repositories)} 个仓库")
            
            # 2. 保存原始数据
            logger.info("步骤 2: 保存原始数据")
            today = datetime.now().strftime('%Y-%m-%d')
            data_file = self.data_manager.save_daily_data(repositories, today)
            logger.info(f"数据已保存到: {data_file}")
            
            # 3. 生成Markdown报告
            logger.info("步骤 3: 生成Markdown报告")
            report_content = self.report_generator.generate_report(repositories)
            report_file = self.report_generator.save_report(report_content)
            logger.info(f"报告已保存到: {report_file}")
            
            # 4. 发送邮件报告
            logger.info("步骤 4: 发送邮件报告")
            if self.email_sender.send_report(report_file):
                logger.info("邮件发送成功")
            else:
                logger.warning("邮件发送失败，请检查邮件配置")

            # 5. 清理旧数据（可选）
            logger.info("步骤 5: 清理旧数据")
            self.data_manager.cleanup_old_data(keep_days=30)

            logger.info("=" * 50)
            logger.info("每日任务执行完成")
            logger.info("=" * 50)

            return True
            
        except Exception as e:
            logger.error(f"执行每日任务时发生错误: {e}")
            return False
    
    def run_manual_task(self, period: str = 'daily'):
        """手动执行任务"""
        try:
            logger.info(f"手动执行爬取任务 (周期: {period})")
            
            # 爬取数据
            repositories = self.spider.crawl_trending_repositories(period)
            
            if not repositories:
                logger.error("未能获取到任何仓库数据")
                return False
            
            # 生成报告
            report_content = self.report_generator.generate_report(repositories)
            
            # 保存到当前目录
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"GitHub热门项目_{period}_{timestamp}.md"
            
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"报告已生成: {report_filename}")
            print(f"\n✅ 报告已生成: {report_filename}")
            print(f"📊 共爬取 {len(repositories)} 个热门项目")
            
            return True
            
        except Exception as e:
            logger.error(f"手动执行任务时发生错误: {e}")
            print(f"❌ 执行失败: {e}")
            return False
    
    def start_scheduler(self):
        """启动定时任务调度器"""
        logger.info(f"启动定时任务调度器，每天 {SCHEDULE_TIME} 执行")
        print(f"🤖 GitHub热门仓库爬虫已启动")
        print(f"⏰ 定时任务: 每天 {SCHEDULE_TIME} 自动执行")
        print(f"📁 数据目录: {DATA_DIR}")
        print(f"📄 报告目录: {REPORTS_DIR}")
        print(f"📋 日志文件: {LOG_FILE}")
        print("按 Ctrl+C 停止程序")
        
        # 设置定时任务
        schedule.every().day.at(SCHEDULE_TIME).do(self.run_daily_task)
        
        # 可选：立即执行一次
        if input("\n是否立即执行一次任务？(y/N): ").lower() == 'y':
            print("立即执行任务...")
            self.run_daily_task()
        
        # 主循环
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("用户中断，程序退出")
            print("\n👋 程序已停止")
    
    def show_statistics(self, days: int = 7):
        """显示统计信息"""
        try:
            logger.info(f"获取最近 {days} 天的统计信息")
            summary = self.data_manager.get_statistics_summary(days)
            
            if not summary:
                print("❌ 暂无统计数据")
                return
            
            print(f"\n📊 最近 {days} 天统计信息")
            print("=" * 40)
            print(f"数据天数: {summary['total_data_points']}")
            print(f"总项目数: {summary['total_repositories']}")
            print(f"日均项目数: {summary['avg_repositories_per_day']}")
            
            if summary['top_languages']:
                print(f"\n🔤 热门编程语言:")
                for lang, count in list(summary['top_languages'].items())[:5]:
                    print(f"  {lang}: {count} 项目")
            
            if summary['most_starred_repo']:
                repo = summary['most_starred_repo']
                print(f"\n🌟 最受欢迎项目: {repo['name']} ({repo['stars']} stars)")
            
            if summary['available_dates']:
                print(f"\n📅 可用数据日期: {', '.join(summary['available_dates'][:5])}")
                if len(summary['available_dates']) > 5:
                    print(f"  ... 等 {len(summary['available_dates'])} 天")
            
        except Exception as e:
            logger.error(f"获取统计信息时发生错误: {e}")
            print(f"❌ 获取统计信息失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GitHub热门仓库爬虫')
    parser.add_argument('--mode', choices=['schedule', 'manual', 'daily', 'stats'],
                       default='schedule', help='运行模式')
    parser.add_argument('--period', choices=['daily', 'weekly', 'monthly'], 
                       default='daily', help='爬取周期（仅manual模式）')
    parser.add_argument('--days', type=int, default=7, 
                       help='统计天数（仅stats模式）')
    
    args = parser.parse_args()
    
    bot = GitHubTrendingBot()
    
    if args.mode == 'schedule':
        # 定时任务模式
        bot.start_scheduler()
        
    elif args.mode == 'manual':
        # 手动执行模式
        print(f"🚀 手动执行爬取任务 (周期: {args.period})")
        success = bot.run_manual_task(args.period)
        sys.exit(0 if success else 1)

    elif args.mode == 'daily':
        # 每日任务模式（用于定时任务，不等待用户输入）
        print("🚀 执行每日任务")
        success = bot.run_daily_task()
        sys.exit(0 if success else 1)

    elif args.mode == 'stats':
        # 统计信息模式
        bot.show_statistics(args.days)


if __name__ == "__main__":
    main()
