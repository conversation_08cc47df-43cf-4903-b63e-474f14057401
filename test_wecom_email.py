"""
企业微信邮箱配置测试
"""

import os
import smtplib
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_wecom_email():
    """测试企业微信邮箱配置"""
    print("📧 测试企业微信邮箱配置")
    print("=" * 40)
    
    # 获取配置
    smtp_server = os.environ.get("EMAIL_SMTP_SERVER")
    smtp_port = int(os.environ.get("EMAIL_SMTP_PORT", "587"))
    from_email = os.environ.get("EMAIL_FROM")
    password = os.environ.get("EMAIL_PASSWORD")
    to_email = os.environ.get("EMAIL_TO")
    
    print(f"SMTP服务器: {smtp_server}")
    print(f"SMTP端口: {smtp_port}")
    print(f"发送邮箱: {from_email}")
    print(f"接收邮箱: {to_email}")
    print(f"密码: {'*' * len(password) if password else '未配置'}")
    
    if not all([smtp_server, from_email, password, to_email]):
        print("❌ 邮件配置不完整")
        return False
    
    try:
        print("\n🔗 测试SMTP连接...")
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_email, password)
        server.quit()
        
        print("✅ 企业微信邮箱连接成功！")
        print("📧 邮件配置正确，可以发送邮件")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n💡 解决建议:")
        print("1. 检查邮箱地址和密码是否正确")
        print("2. 确认企业微信邮箱已启用SMTP服务")
        print("3. 检查网络连接")
        return False

if __name__ == "__main__":
    test_wecom_email()
