"""
测试邮件配置和发送功能
"""

import os
from dotenv import load_dotenv
from email_sender import EmailSender

# 加载环境变量
load_dotenv()

def test_email_configuration():
    """测试邮件配置"""
    print("📧 测试邮件配置")
    print("=" * 40)
    
    # 检查环境变量
    config_items = [
        ("EMAIL_SMTP_SERVER", os.environ.get("EMAIL_SMTP_SERVER")),
        ("EMAIL_SMTP_PORT", os.environ.get("EMAIL_SMTP_PORT")),
        ("EMAIL_FROM", os.environ.get("EMAIL_FROM")),
        ("EMAIL_PASSWORD", os.environ.get("EMAIL_PASSWORD")),
        ("EMAIL_TO", os.environ.get("EMAIL_TO"))
    ]
    
    all_configured = True
    for key, value in config_items:
        if value:
            if key == "EMAIL_PASSWORD":
                print(f"✅ {key}: {'*' * len(value)}")
            else:
                print(f"✅ {key}: {value}")
        else:
            print(f"❌ {key}: 未配置")
            all_configured = False
    
    return all_configured

def test_email_connection():
    """测试邮件服务器连接"""
    print("\n🔗 测试邮件服务器连接")
    print("=" * 40)
    
    sender = EmailSender()
    
    if sender.test_connection():
        print("✅ 邮件服务器连接成功")
        return True
    else:
        print("❌ 邮件服务器连接失败")
        print("💡 请检查:")
        print("   - 企业微信邮箱地址和密码是否正确")
        print("   - 网络连接是否正常")
        print("   - SMTP服务器配置是否正确")
        return False

def test_email_sending():
    """测试邮件发送"""
    print("\n📤 测试邮件发送")
    print("=" * 40)
    
    # 查找测试报告文件
    import glob
    report_files = glob.glob("reports/*.md")
    
    if not report_files:
        print("❌ 未找到报告文件")
        print("💡 请先运行: python daily_task.py")
        return False
    
    # 使用最新的报告文件
    latest_report = max(report_files, key=os.path.getctime)
    print(f"📄 使用报告文件: {latest_report}")
    
    sender = EmailSender()
    
    if sender.send_report(latest_report, "GitHub热门项目日报 - 测试邮件"):
        print("✅ 测试邮件发送成功")
        print(f"📧 邮件已发送到: {sender.to_email}")
        return True
    else:
        print("❌ 测试邮件发送失败")
        return False

def main():
    """主测试函数"""
    print("🚀 邮件功能测试")
    print("=" * 50)
    
    # 1. 测试配置
    config_ok = test_email_configuration()
    
    if not config_ok:
        print("\n⚠️  邮件配置不完整，请先配置.env文件")
        print("📖 参考: EMAIL_SETUP_GUIDE.md")
        return
    
    # 2. 测试连接
    connection_ok = test_email_connection()
    
    if not connection_ok:
        print("\n⚠️  邮件服务器连接失败，请检查配置")
        return
    
    # 3. 测试发送
    sending_ok = test_email_sending()
    
    print("\n" + "=" * 50)
    if config_ok and connection_ok and sending_ok:
        print("🎉 所有测试通过！邮件功能配置成功")
        print("\n📋 下一步:")
        print("1. 运行定时任务: python daily_task.py")
        print("2. 设置Windows定时任务: setup_scheduler.bat")
        print("3. 或使用Code Runner手动执行")
    else:
        print("⚠️  部分测试失败，请检查配置")
        print("📖 详细指南: EMAIL_SETUP_GUIDE.md")

if __name__ == "__main__":
    main()
