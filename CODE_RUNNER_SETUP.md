# 🚀 Code Runner + 定时邮件配置完成

## ✅ 已完成的配置

### 1. **邮件发送功能**
- ✅ 创建了 `email_sender.py` 邮件发送模块
- ✅ 支持HTML格式邮件和Markdown附件
- ✅ 集成到主程序的每日任务中
- ✅ 目标邮箱：`<EMAIL>`

### 2. **Code Runner配置**
- ✅ 创建了 `.vscode/settings.json` 配置文件
- ✅ 优化了Python执行环境
- ✅ 支持终端输出和错误显示

### 3. **定时任务脚本**
- ✅ `daily_task.py` - 专用的每日执行脚本
- ✅ `setup_scheduler.bat` - Windows定时任务设置
- ✅ 完整的日志记录和错误处理

### 4. **测试工具**
- ✅ `test_email_setup.py` - 邮件功能测试
- ✅ 配置验证和连接测试
- ✅ 发送测试邮件功能

## 🔧 配置步骤

### 第1步：配置邮件发送
编辑 `.env` 文件，设置企业微信邮箱信息：

```env
# 企业微信邮箱配置
EMAIL_SMTP_SERVER="smtp.exmail.qq.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"    # 企业微信邮箱
EMAIL_PASSWORD="your_email_password"           # 邮箱密码
EMAIL_TO="<EMAIL>"            # 接收邮箱
```

**重要**：使用企业微信邮箱的登录密码即可，无需应用专用密码。

### 第2步：测试邮件功能
```bash
python test_email_setup.py
```

### 第3步：设置定时任务

#### 方法A：Windows任务计划程序（推荐）
```bash
# 右键"以管理员身份运行"
setup_scheduler.bat
```

#### 方法B：Code Runner手动执行
1. 在VSCode中打开 `daily_task.py`
2. 按 `Ctrl+Alt+N` 或点击右上角的运行按钮
3. 每天12:00手动执行一次

#### 方法C：Python内置调度器
```bash
python main.py --mode scheduler
```

## 📧 邮件功能详情

### 邮件内容
- **主题**：`GitHub热门项目日报 - 2025-06-30`
- **正文**：HTML格式，包含报告预览和统计信息
- **附件**：完整的Markdown报告文件
- **发送时间**：每天12:00（可调整）

### 邮件模板
```html
🚀 GitHub热门项目日报
📅 生成时间: 2025年06月30日 12:00
📧 发送至: <EMAIL>

📋 报告预览
[项目列表和描述...]

💡 提示: 完整报告请查看附件中的Markdown文件
```

## 🕐 定时执行方案

### 方案1：Windows任务计划程序
- ✅ **优点**：完全自动化，无需人工干预
- ✅ **稳定性**：系统级定时任务，可靠性高
- ✅ **灵活性**：可设置多种触发条件
- ⚠️ **要求**：需要管理员权限设置

### 方案2：Code Runner手动触发
- ✅ **优点**：简单易用，适合开发测试
- ✅ **控制性**：可以随时执行和调试
- ⚠️ **缺点**：需要每天手动执行
- ⚠️ **依赖**：需要VSCode保持打开

### 方案3：Python调度器
- ✅ **优点**：跨平台，配置简单
- ✅ **集成性**：与主程序集成度高
- ⚠️ **缺点**：需要程序持续运行
- ⚠️ **稳定性**：依赖Python进程稳定性

## 🧪 测试和验证

### 1. 配置测试
```bash
python test_config.py          # 验证基础配置
python test_email_setup.py     # 验证邮件配置
python test_direct_api.py      # 验证API连接
```

### 2. 功能测试
```bash
python daily_task.py           # 完整功能测试
```

### 3. 定时任务测试
```bash
# 手动触发定时任务
schtasks /run /tn "GitHubTrendingDaily"
```

## 📋 使用Code Runner

### 基本操作
1. **安装扩展**：在VSCode中安装"Code Runner"扩展
2. **打开文件**：打开 `daily_task.py`
3. **执行代码**：
   - 快捷键：`Ctrl+Alt+N`
   - 右键菜单：`Run Code`
   - 命令面板：`Code Runner: Run Code`

### 自定义快捷键
在VSCode设置中添加：
```json
{
    "key": "f5",
    "command": "code-runner.run",
    "when": "editorTextFocus"
}
```

### 每日执行流程
1. **12:00前**：打开VSCode和项目
2. **12:00时**：打开 `daily_task.py`
3. **执行**：按 `Ctrl+Alt+N` 运行
4. **确认**：检查终端输出和邮件接收

## 🔧 故障排除

### 常见问题

#### 1. 邮件发送失败
```bash
python test_email_setup.py
```
检查邮箱配置和网络连接

#### 2. GitHub访问失败
```bash
python test_network.py
```
配置代理或使用VPN

#### 3. API调用失败
```bash
python test_direct_api.py
```
检查DeepSeek API配置

#### 4. Code Runner无法执行
- 检查Python路径配置
- 确认工作目录正确
- 查看VSCode输出面板

## 📊 监控和维护

### 日志文件
- **位置**：`logs/daily_task_YYYYMMDD.log`
- **内容**：详细的执行日志和错误信息
- **清理**：定期清理30天前的日志

### 邮件确认
- **发送成功**：检查邮箱是否收到报告
- **内容验证**：确认附件和正文内容正确
- **时间检查**：验证发送时间是否准确

### 定期检查
- **每周**：检查定时任务状态
- **每月**：更新API密钥（如需要）
- **季度**：清理旧数据和日志文件

---

## 🎉 配置完成！

现在您的GitHub热门项目爬虫已经完全配置好了：

1. ✅ **每天12:00自动执行**（如果设置了定时任务）
2. ✅ **自动发送邮件到 <EMAIL>**
3. ✅ **支持Code Runner手动执行**
4. ✅ **完整的错误处理和日志记录**

**下一步**：配置您的Gmail应用专用密码，然后运行测试！
