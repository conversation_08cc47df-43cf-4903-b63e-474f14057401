# GitHub 今日热门项目

> 每日精选优质开源项目

**📅 日期**: 2025-06-30  
**🕐 更新时间**: 2025-06-30 13:42:52  
**📊 项目数量**: 16

---

## 📊 今日统计

- **总项目数**: 16
- **总星数**: 380.4K
- **今日新增星数**: 4.5K
- **平均星数**: 23.8K

**🌟 最受欢迎项目**: [microsoft /

      generative-ai-for-beginners](https://github.com/microsoft/generative-ai-for-beginners) (86.8K stars)

**🔥 今日最热项目**: [twentyhq /

      twenty](https://github.com/twentyhq/twenty) (+1.0K stars today)

### 🔤 编程语言分布

- **Python**: 5 项目 (31.2%)
- **TypeScript**: 4 项目 (25.0%)
- **Rust**: 1 项目 (6.2%)
- **HTML**: 1 项目 (6.2%)
- **Jupyter Notebook**: 1 项目 (6.2%)
- **Java**: 1 项目 (6.2%)
- **Go**: 1 项目 (6.2%)
- **Vue**: 1 项目 (6.2%)

---

## 🚀 热门项目

### 1. [twentyhq /

      twenty](https://github.com/twentyhq/twenty)

**📝 描述**: Building a modern alternative to Salesforce, powered by the community.

**💻 语言**: TypeScript | **⭐ Stars**: 31.6K | **🍴 Forks**: 3.5K | **📈 今日**: +1.0K stars

---

### 2. [GraphiteEditor /

      Graphite](https://github.com/GraphiteEditor/Graphite)

**📝 描述**: 2D vector & raster editor that melds traditional layers & tools with a modern node-based, non-destructive, procedural workflow.

**💻 语言**: Rust | **⭐ Stars**: 14.7K | **🍴 Forks**: 674 | **📈 今日**: +374 stars

---

### 3. [octra-labs /

      wallet-gen](https://github.com/octra-labs/wallet-gen)

**📝 描述**: 暂无描述

**💻 语言**: HTML | **⭐ Stars**: 320 | **🍴 Forks**: 6.3K | **📈 今日**: +68 stars

---

### 4. [microsoft /

      generative-ai-for-beginners](https://github.com/microsoft/generative-ai-for-beginners)

**📝 描述**: 21 Lessons, Get Started Building with Generative AI 🔗 https://microsoft.github.io/generative-ai-for-beginners/

**💻 语言**: Jupyter Notebook | **⭐ Stars**: 86.8K | **🍴 Forks**: 45.6K | **📈 今日**: +296 stars

---

### 5. [x1xhlol /

      system-prompts-and-models-of-ai-tools](https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools)

**📝 描述**: FULL v0, Cursor, Manus, Same.dev, Lovable, Devin, Replit Agent, Windsurf Agent, VSCode Agent, Dia Browser, Trae AI & Cluely (And other Open Sourced) System Prompts, Tools & AI Models.

**⭐ Stars**: 63.2K | **🍴 Forks**: 18.5K | **📈 今日**: +866 stars

---

### 6. [coleam00 /

      ottomator-agents](https://github.com/coleam00/ottomator-agents)

**📝 描述**: All the open source AI Agents hosted on the oTTomator Live Agent Studio platform!

**💻 语言**: Python | **⭐ Stars**: 3.1K | **🍴 Forks**: 1.2K | **📈 今日**: +164 stars

---

### 7. [stanford-oval /

      storm](https://github.com/stanford-oval/storm)

**📝 描述**: An LLM-powered knowledge curation system that researches a topic and generates a full-length report with citations.

**💻 语言**: Python | **⭐ Stars**: 25.3K | **🍴 Forks**: 2.3K | **📈 今日**: +154 stars

---

### 8. [jnsahaj /

      tweakcn](https://github.com/jnsahaj/tweakcn)

**📝 描述**: A visual no-code theme editor for shadcn/ui components

**💻 语言**: TypeScript | **⭐ Stars**: 4.6K | **🍴 Forks**: 237 | **📈 今日**: +126 stars

**👥 主要贡献者**: [@lovable-dev](https://github.com/lovable-dev)

---

### 9. [mendableai /

      firecrawl](https://github.com/mendableai/firecrawl)

**📝 描述**: 🔥 Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.

**💻 语言**: TypeScript | **⭐ Stars**: 41.1K | **🍴 Forks**: 3.9K | **📈 今日**: +176 stars

---

### 10. [ItzCrazyKns /

      Perplexica](https://github.com/ItzCrazyKns/Perplexica)

**📝 描述**: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI

**💻 语言**: TypeScript | **⭐ Stars**: 22.8K | **🍴 Forks**: 2.4K | **📈 今日**: +26 stars

---

### 11. [adityachandelgit /

      BookLore](https://github.com/adityachandelgit/BookLore)

**📝 描述**: BookLore is a web app for hosting, managing, and exploring books, with support for PDFs, eBooks, reading progress, metadata, and stats.

**💻 语言**: Java | **⭐ Stars**: 2.2K | **🍴 Forks**: 88 | **📈 今日**: +299 stars

---

### 12. [swisskyrepo /

      PayloadsAllTheThings](https://github.com/swisskyrepo/PayloadsAllTheThings)

**📝 描述**: A list of useful payloads and bypass for Web Application Security and Pentest/CTF

**💻 语言**: Python | **⭐ Stars**: 66.6K | **🍴 Forks**: 15.5K | **📈 今日**: +154 stars

---

### 13. [mikumifa /

      biliTickerBuy](https://github.com/mikumifa/biliTickerBuy)

**📝 描述**: b站会员购购票辅助工具

**💻 语言**: Python | **⭐ Stars**: 2.5K | **🍴 Forks**: 335 | **📈 今日**: +63 stars

**👥 主要贡献者**: [@github-actions](https://github.com/github-actions)

---

### 14. [m1k1o /

      neko](https://github.com/m1k1o/neko)

**📝 描述**: A self hosted virtual browser that runs in docker and uses WebRTC.

**💻 语言**: Go | **⭐ Stars**: 11.4K | **🍴 Forks**: 759 | **📈 今日**: +297 stars

---

### 15. [LMCache /

      LMCache](https://github.com/LMCache/LMCache)

**📝 描述**: Supercharge Your LLM with the Fastest KV Cache Layer

**💻 语言**: Python | **⭐ Stars**: 2.2K | **🍴 Forks**: 272 | **📈 今日**: +276 stars

---

### 16. [zyronon /

      typing-word](https://github.com/zyronon/typing-word)

**📝 描述**: 在网页上背单词

**💻 语言**: Vue | **⭐ Stars**: 2.0K | **🍴 Forks**: 234 | **📈 今日**: +110 stars

---

## 📌 说明

- 数据来源: [GitHub Trending](https://github.com/trending)
- 更新频率: 每日 12:00 自动更新
- 项目排序: 按GitHub热门程度排序

## 🔗 相关链接

- [GitHub Trending](https://github.com/trending)
- [GitHub Trending - Weekly](https://github.com/trending?since=weekly)
- [GitHub Trending - Monthly](https://github.com/trending?since=monthly)

---

*本报告由 GitHub Spider 自动生成 | 生成时间: 2025-06-30 13:42:52*
