# 📧 邮件配置指南

## 🚀 快速配置

### 1. 配置发送邮箱

编辑 `.env` 文件，设置您的邮箱信息：

```env
# 邮件配置
EMAIL_SMTP_SERVER="smtp.gmail.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"          # 替换为您的Gmail地址
EMAIL_PASSWORD="your_app_password"         # 替换为您的应用专用密码
EMAIL_TO="<EMAIL>"        # 接收邮箱（已设置）
```

### 2. 获取Gmail应用专用密码

#### 步骤1: 启用两步验证
1. 访问 [Google账户设置](https://myaccount.google.com/)
2. 点击"安全性"
3. 启用"两步验证"

#### 步骤2: 生成应用专用密码
1. 在"安全性"页面，找到"应用专用密码"
2. 选择"邮件"和"Windows计算机"
3. 点击"生成"
4. 复制生成的16位密码（格式：xxxx xxxx xxxx xxxx）
5. 将此密码填入 `.env` 文件的 `EMAIL_PASSWORD`

### 3. 其他邮箱配置

#### QQ邮箱
```env
EMAIL_SMTP_SERVER="smtp.qq.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"
EMAIL_PASSWORD="your_authorization_code"   # QQ邮箱授权码
```

#### 163邮箱
```env
EMAIL_SMTP_SERVER="smtp.163.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"
EMAIL_PASSWORD="your_authorization_code"   # 163邮箱授权码
```

#### Outlook邮箱
```env
EMAIL_SMTP_SERVER="smtp-mail.outlook.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"
EMAIL_PASSWORD="your_password"             # Outlook密码
```

## 🧪 测试邮件功能

### 1. 测试邮件配置
```bash
python -c "from email_sender import EmailSender; sender = EmailSender(); print('✅ 连接成功' if sender.test_connection() else '❌ 连接失败')"
```

### 2. 发送测试邮件
```bash
python email_sender.py
```

### 3. 完整功能测试
```bash
python daily_task.py
```

## 🕐 设置定时任务

### 方法1: Windows任务计划程序（推荐）

1. **自动设置**（管理员权限）:
   ```bash
   # 右键"以管理员身份运行"
   setup_scheduler.bat
   ```

2. **手动设置**:
   - 打开"任务计划程序"
   - 创建基本任务
   - 名称：`GitHubTrendingDaily`
   - 触发器：每天12:00
   - 操作：启动程序
   - 程序：`python`
   - 参数：`daily_task.py`
   - 起始于：项目目录路径

### 方法2: Code Runner + 手动触发

1. **安装Code Runner扩展**
2. **配置快捷键**:
   - 打开VSCode设置
   - 搜索"Code Runner"
   - 设置快捷键（如Ctrl+F5）

3. **每日执行**:
   - 打开 `daily_task.py`
   - 按快捷键执行

### 方法3: Python内置调度器

运行持续调度器：
```bash
python main.py --mode scheduler
```

## 📋 邮件内容预览

每日邮件将包含：

### 📧 邮件主题
```
GitHub热门项目日报 - 2025-06-30
```

### 📄 邮件内容
- **HTML格式正文**：报告预览和摘要
- **Markdown附件**：完整的项目报告
- **发送信息**：时间戳和接收确认

### 📊 报告内容
- 当日GitHub热门项目列表
- 项目描述（中文翻译）
- 技术栈和统计信息
- 项目链接和作者信息

## 🔧 故障排除

### 常见问题

#### 1. 邮件发送失败
```
❌ 邮件发送失败: (535, '5.7.8 Username and Password not accepted')
```
**解决方案**：
- 检查邮箱地址和密码
- 确认使用应用专用密码（不是登录密码）
- 验证两步验证已启用

#### 2. SMTP连接超时
```
❌ 邮件服务器连接测试失败: timed out
```
**解决方案**：
- 检查网络连接
- 确认SMTP服务器地址和端口
- 尝试使用代理或VPN

#### 3. 附件过大
```
❌ 邮件发送失败: Message too large
```
**解决方案**：
- 报告文件通常很小，检查是否有其他大文件
- 清理logs目录中的大日志文件

### 调试步骤

1. **检查配置**:
   ```bash
   python test_config.py
   ```

2. **测试网络**:
   ```bash
   python test_network.py
   ```

3. **测试API**:
   ```bash
   python test_direct_api.py
   ```

4. **测试邮件**:
   ```bash
   python email_sender.py
   ```

## 🔒 安全提醒

1. **保护密码**：
   - 不要将 `.env` 文件提交到Git
   - 定期更换应用专用密码
   - 不要在代码中硬编码密码

2. **权限控制**：
   - 使用应用专用密码而非主密码
   - 定期检查邮箱登录记录
   - 及时撤销不需要的应用权限

3. **备份配置**：
   - 备份 `.env` 文件到安全位置
   - 记录重要的配置信息

---

**配置完成后，您将每天12:00收到GitHub热门项目报告！** 📧✨
