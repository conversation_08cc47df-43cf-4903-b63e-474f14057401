"""
邮件发送模块
支持发送GitHub热门项目报告
"""

import smtplib
import os
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class EmailSender:
    """邮件发送器"""
    
    def __init__(self):
        """初始化邮件配置"""
        self.smtp_server = os.environ.get("EMAIL_SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.environ.get("EMAIL_SMTP_PORT", "587"))
        self.from_email = os.environ.get("EMAIL_FROM")
        self.password = os.environ.get("EMAIL_PASSWORD")
        self.to_email = os.environ.get("EMAIL_TO", "<EMAIL>")
        
        # 验证配置
        if not self.from_email or not self.password:
            logger.warning("邮件配置不完整，请检查.env文件中的EMAIL_FROM和EMAIL_PASSWORD")
    
    def send_report(self, report_file_path: str, subject: str = None) -> bool:
        """
        发送GitHub热门项目报告
        
        Args:
            report_file_path: 报告文件路径
            subject: 邮件主题（可选）
            
        Returns:
            发送是否成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(report_file_path):
                logger.error(f"报告文件不存在: {report_file_path}")
                return False
            
            # 读取报告内容
            with open(report_file_path, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            # 生成邮件主题
            if not subject:
                today = datetime.now().strftime('%Y-%m-%d')
                subject = f"GitHub热门项目日报 - {today}"
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = self.to_email
            msg['Subject'] = subject
            
            # 邮件正文
            body = self._create_email_body(report_content)
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # 添加附件
            self._attach_file(msg, report_file_path)
            
            # 发送邮件
            return self._send_email(msg)
            
        except Exception as e:
            logger.error(f"发送邮件时出错: {e}")
            return False
    
    def _create_email_body(self, report_content: str) -> str:
        """创建邮件正文"""
        # 提取报告摘要（前500字符）
        summary = report_content[:500] + "..." if len(report_content) > 500 else report_content
        
        # 转换Markdown为简单HTML
        html_content = summary.replace('\n', '<br>')
        html_content = html_content.replace('# ', '<h1>').replace('\n', '</h1>\n')
        html_content = html_content.replace('## ', '<h2>').replace('\n', '</h2>\n')
        html_content = html_content.replace('### ', '<h3>').replace('\n', '</h3>\n')
        
        body = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background-color: #f4f4f4; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .footer {{ background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 12px; }}
                h1, h2, h3 {{ color: #2c3e50; }}
                .highlight {{ background-color: #e8f4fd; padding: 10px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 GitHub热门项目日报</h1>
                <p>每日精选优质开源项目</p>
            </div>
            
            <div class="content">
                <div class="highlight">
                    <p><strong>📅 生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M')}</p>
                    <p><strong>📧 发送至:</strong> {self.to_email}</p>
                </div>
                
                <h2>📋 报告预览</h2>
                <div style="border-left: 4px solid #3498db; padding-left: 15px; margin: 20px 0;">
                    {html_content}
                </div>
                
                <p><strong>💡 提示:</strong> 完整报告请查看附件中的Markdown文件。</p>
            </div>
            
            <div class="footer">
                <p>🤖 由GitHub热门项目爬虫自动生成 | ⏰ 每日12:00定时发送</p>
                <p>如有问题，请检查程序日志或联系管理员</p>
            </div>
        </body>
        </html>
        """
        return body
    
    def _attach_file(self, msg: MIMEMultipart, file_path: str):
        """添加文件附件"""
        try:
            with open(file_path, 'rb') as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            
            filename = os.path.basename(file_path)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            logger.info(f"已添加附件: {filename}")
            
        except Exception as e:
            logger.error(f"添加附件失败: {e}")
    
    def _send_email(self, msg: MIMEMultipart) -> bool:
        """发送邮件"""
        try:
            # 连接SMTP服务器
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # 启用TLS加密
            server.login(self.from_email, self.password)

            # 发送邮件
            text = msg.as_string()
            server.sendmail(self.from_email, self.to_email, text)
            server.quit()

            logger.info(f"邮件发送成功: {self.from_email} -> {self.to_email}")
            return True

        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False
    
    def test_connection(self) -> bool:
        """测试邮件服务器连接"""
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.from_email, self.password)
            server.quit()

            logger.info("邮件服务器连接测试成功")
            return True

        except Exception as e:
            logger.error(f"邮件服务器连接测试失败: {e}")
            return False

def test_email_sender():
    """测试邮件发送功能"""
    print("📧 测试邮件发送功能...")
    
    sender = EmailSender()
    
    # 测试连接
    if sender.test_connection():
        print("✅ 邮件服务器连接成功")
    else:
        print("❌ 邮件服务器连接失败")
        return
    
    # 查找最新的报告文件
    import glob
    report_files = glob.glob("reports/*.md")
    if report_files:
        latest_report = max(report_files, key=os.path.getctime)
        print(f"📄 找到报告文件: {latest_report}")
        
        if sender.send_report(latest_report, "GitHub热门项目日报 - 测试"):
            print("✅ 测试邮件发送成功")
        else:
            print("❌ 测试邮件发送失败")
    else:
        print("❌ 未找到报告文件")

if __name__ == "__main__":
    test_email_sender()
