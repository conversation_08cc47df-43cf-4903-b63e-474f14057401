@echo off
chcp 65001 >nul
echo ========================================
echo 🕐 设置GitHub热门项目定时任务
echo ========================================

set TASK_NAME=GitHubTrendingDaily
set SCRIPT_PATH=%~dp0daily_task.py
set PYTHON_PATH=python

echo 📋 任务信息:
echo    任务名称: %TASK_NAME%
echo    脚本路径: %SCRIPT_PATH%
echo    执行时间: 每天12:00
echo    目标邮箱: <EMAIL>
echo.

echo 🔍 检查是否已存在同名任务...
schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  发现已存在的任务，正在删除...
    schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1
    echo ✅ 旧任务已删除
)

echo 📅 创建新的定时任务...
schtasks /create /tn "%TASK_NAME%" /tr "%PYTHON_PATH% \"%SCRIPT_PATH%\"" /sc daily /st 12:00 /f

if %errorlevel% equ 0 (
    echo ✅ 定时任务创建成功！
    echo.
    echo 📋 任务详情:
    schtasks /query /tn "%TASK_NAME%" /fo list
    echo.
    echo 💡 提示:
    echo    - 任务将在每天12:00自动执行
    echo    - 可以通过"任务计划程序"管理任务
    echo    - 如需手动执行: schtasks /run /tn "%TASK_NAME%"
    echo    - 如需删除任务: schtasks /delete /tn "%TASK_NAME%" /f
) else (
    echo ❌ 定时任务创建失败！
    echo 💡 请以管理员身份运行此脚本
)

echo.
echo ========================================
pause
