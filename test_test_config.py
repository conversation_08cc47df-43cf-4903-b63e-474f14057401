"""
测试配置是否正确
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

print("🔍 配置测试")
print("=" * 40)

# 检查环境变量
api_key = os.environ.get("DEEPSEEK_API_KEY")
print(f"📋 .env文件加载: {'✅ 成功' if api_key else '❌ 失败'}")
print(f"🔑 API密钥: {api_key[:20] + '...' if api_key else '未找到'}")

# 检查依赖
try:
    from openai import OpenAI
    print("📦 OpenAI库: ✅ 已安装")
except ImportError:
    print("📦 OpenAI库: ❌ 未安装")

try:
    import requests
    print("📦 Requests库: ✅ 已安装")
except ImportError:
    print("📦 Requests库: ❌ 未安装")

# 测试API客户端初始化
if api_key:
    try:
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        print("🤖 API客户端: ✅ 初始化成功")
        print(f"📡 Base URL: {client.base_url}")
    except Exception as e:
        print(f"🤖 API客户端: ❌ 初始化失败 - {e}")
else:
    print("🤖 API客户端: ❌ 无法初始化（缺少API密钥）")

print("=" * 40)
print("配置检查完成!")

if api_key:
    print("\n💡 建议: 运行 'python test_direct_api.py' 测试API连接")
else:
    print("\n⚠️  请检查 .env 文件是否存在且包含正确的 DEEPSEEK_API_KEY")
