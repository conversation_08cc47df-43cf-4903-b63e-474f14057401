# check_config.py
# 快速检查配置是否正确

import os
from dotenv import load_dotenv

load_dotenv()

def check_email_config():
    """检查邮件配置"""
    print("🔍 检查邮件配置...")
    print("=" * 40)
    
    from_email = os.environ.get("EMAIL_FROM")
    password = os.environ.get("EMAIL_PASSWORD")
    smtp_server = os.environ.get("EMAIL_SMTP_SERVER")
    
    # 检查邮箱格式
    if from_email == "<EMAIL>":
        print("❌ 错误：EMAIL_FROM 还是占位符，需要改成真实邮箱")
        return False
    
    if password == "your_app_password":
        print("❌ 错误：EMAIL_PASSWORD 还是占位符，需要改成真实密码")
        return False
    
    # 检查邮箱类型匹配
    if "@gmail.com" in from_email and smtp_server != "smtp.gmail.com":
        print("❌ 错误：Gmail邮箱应该用 smtp.gmail.com")
        return False
    
    if from_email and "@gmail.com" not in from_email and smtp_server == "smtp.gmail.com":
        print("❌ 错误：企业邮箱应该用 smtp.exmail.qq.com")
        return False
    
    print(f"✅ 邮箱地址：{from_email}")
    print(f"✅ SMTP服务器：{smtp_server}")
    print(f"✅ 密码长度：{len(password) if password else 0}位")
    
    return True

if __name__ == "__main__":
    if check_email_config():
        print("\n🎉 配置检查通过！可以测试发送邮件了")
    else:
        print("\n⚠️  请修改 .env 文件后重新测试")