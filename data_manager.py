"""
数据管理模块
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from config import *

logger = logging.getLogger(__name__)


class DataManager:
    """数据管理类"""
    
    def __init__(self):
        # 确保数据目录存在
        os.makedirs(DATA_DIR, exist_ok=True)
        os.makedirs(REPORTS_DIR, exist_ok=True)
    
    def save_daily_data(self, repositories: List[Dict], date: str = None) -> str:
        """
        保存每日数据
        
        Args:
            repositories: 仓库信息列表
            date: 日期字符串 (YYYY-MM-DD)，默认为今天
            
        Returns:
            保存的文件路径
        """
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
        
        filename = DATA_FILE_TEMPLATE.format(date=date)
        
        data = {
            'date': date,
            'crawl_time': datetime.now().isoformat(),
            'total_count': len(repositories),
            'repositories': repositories
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            raise
    
    def load_daily_data(self, date: str) -> Optional[Dict]:
        """
        加载指定日期的数据
        
        Args:
            date: 日期字符串 (YYYY-MM-DD)
            
        Returns:
            数据字典，失败返回None
        """
        filename = DATA_FILE_TEMPLATE.format(date=date)
        
        if not os.path.exists(filename):
            logger.warning(f"数据文件不存在: {filename}")
            return None
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"成功加载数据: {filename}")
            return data
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return None
    
    def get_available_dates(self) -> List[str]:
        """
        获取所有可用的数据日期
        
        Returns:
            日期列表，按时间倒序排列
        """
        dates = []
        
        if not os.path.exists(DATA_DIR):
            return dates
        
        for filename in os.listdir(DATA_DIR):
            if filename.startswith('trending_') and filename.endswith('.json'):
                # 提取日期部分
                date_part = filename.replace('trending_', '').replace('.json', '')
                try:
                    # 验证日期格式
                    datetime.strptime(date_part, '%Y-%m-%d')
                    dates.append(date_part)
                except ValueError:
                    continue
        
        # 按日期倒序排列
        dates.sort(reverse=True)
        return dates
    
    def get_recent_data(self, days: int = 7) -> List[Dict]:
        """
        获取最近几天的数据
        
        Args:
            days: 天数
            
        Returns:
            数据列表
        """
        recent_data = []
        today = datetime.now()
        
        for i in range(days):
            date = (today - timedelta(days=i)).strftime('%Y-%m-%d')
            data = self.load_daily_data(date)
            if data:
                recent_data.append(data)
        
        return recent_data
    
    def cleanup_old_data(self, keep_days: int = 30):
        """
        清理旧数据
        
        Args:
            keep_days: 保留天数
        """
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        deleted_count = 0
        
        for filename in os.listdir(DATA_DIR):
            if filename.startswith('trending_') and filename.endswith('.json'):
                date_part = filename.replace('trending_', '').replace('.json', '')
                try:
                    file_date = datetime.strptime(date_part, '%Y-%m-%d')
                    if file_date < cutoff_date:
                        file_path = os.path.join(DATA_DIR, filename)
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除旧数据文件: {filename}")
                except ValueError:
                    continue
        
        logger.info(f"清理完成，删除了 {deleted_count} 个旧数据文件")
    
    def get_statistics_summary(self, days: int = 7) -> Dict:
        """
        获取统计摘要
        
        Args:
            days: 统计天数
            
        Returns:
            统计信息字典
        """
        recent_data = self.get_recent_data(days)
        
        if not recent_data:
            return {}
        
        total_repos = sum(data['total_count'] for data in recent_data)
        avg_repos_per_day = total_repos / len(recent_data)
        
        # 语言统计
        all_languages = []
        for data in recent_data:
            for repo in data['repositories']:
                if repo['language'] != '未知':
                    all_languages.append(repo['language'])
        
        from collections import Counter
        language_stats = Counter(all_languages)
        
        # 最受欢迎的项目
        all_repos = []
        for data in recent_data:
            all_repos.extend(data['repositories'])
        
        if all_repos:
            most_starred = max(all_repos, key=lambda x: x['stars'])
            most_today_starred = max(all_repos, key=lambda x: x['today_stars'])
        else:
            most_starred = None
            most_today_starred = None
        
        summary = {
            'period_days': days,
            'total_data_points': len(recent_data),
            'total_repositories': total_repos,
            'avg_repositories_per_day': round(avg_repos_per_day, 1),
            'top_languages': dict(language_stats.most_common(10)),
            'most_starred_repo': most_starred,
            'most_today_starred_repo': most_today_starred,
            'available_dates': [data['date'] for data in recent_data]
        }
        
        return summary
    
    def export_data(self, start_date: str, end_date: str, output_file: str):
        """
        导出指定时间范围的数据
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            output_file: 输出文件路径
        """
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        export_data = []
        current_dt = start_dt
        
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y-%m-%d')
            data = self.load_daily_data(date_str)
            if data:
                export_data.append(data)
            current_dt += timedelta(days=1)
        
        export_summary = {
            'export_time': datetime.now().isoformat(),
            'date_range': {
                'start': start_date,
                'end': end_date
            },
            'total_days': len(export_data),
            'data': export_data
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已导出到: {output_file}")
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            raise


if __name__ == "__main__":
    # 测试代码
    manager = DataManager()
    
    # 测试保存数据
    test_repos = [
        {
            'name': 'test/repo',
            'url': 'https://github.com/test/repo',
            'description': '测试仓库',
            'language': 'Python',
            'stars': 1000,
            'forks': 200,
            'today_stars': 50,
            'authors': ['testuser'],
            'crawl_time': datetime.now().isoformat()
        }
    ]
    
    # 保存测试数据
    manager.save_daily_data(test_repos)
    
    # 获取可用日期
    dates = manager.get_available_dates()
    print(f"可用日期: {dates}")
    
    # 获取统计摘要
    summary = manager.get_statistics_summary()
    print(f"统计摘要: {summary}")
