"""
GitHub热门仓库爬虫
"""

import requests
import time
import logging
import re
from bs4 import BeautifulSoup
from datetime import datetime
from typing import List, Dict, Optional
from config import *

# 设置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GitHubSpider:
    """GitHub热门仓库爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(REQUEST_HEADERS)
    
    def fetch_page(self, url: str) -> Optional[str]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            
        Returns:
            网页HTML内容，失败返回None
        """
        for attempt in range(MAX_RETRIES):
            try:
                logger.info(f"正在获取页面: {url} (尝试 {attempt + 1}/{MAX_RETRIES})")
                response = self.session.get(url, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()
                response.encoding = 'utf-8'
                logger.info(f"成功获取页面，状态码: {response.status_code}")
                return response.text
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY)
                else:
                    logger.error(f"获取页面最终失败: {url}")
                    return None
    
    def parse_repository_info(self, repo_element) -> Optional[Dict]:
        """
        解析单个仓库信息
        
        Args:
            repo_element: BeautifulSoup元素
            
        Returns:
            仓库信息字典
        """
        try:
            # 提取仓库名称和链接
            title_element = repo_element.find('h2', class_='h3 lh-condensed')
            if not title_element:
                return None
                
            link_element = title_element.find('a')
            if not link_element:
                return None
                
            repo_name = link_element.get_text().strip().replace('\n', '').replace('\r', '').replace('  ', ' ')
            repo_url = "https://github.com" + link_element.get('href')
            
            # 提取描述
            desc_element = repo_element.find('p', class_='col-9 color-fg-muted my-1 pr-4')
            description = desc_element.get_text().strip() if desc_element else "暂无描述"
            
            # 提取编程语言
            language_element = repo_element.find('span', {'itemprop': 'programmingLanguage'})
            language = language_element.get_text().strip() if language_element else "未知"
            
            # 提取星数
            stars_element = repo_element.find('a', href=re.compile(r'/stargazers$'))
            stars = 0
            if stars_element:
                stars_text = stars_element.get_text().strip()
                stars = self._parse_number(stars_text)
            
            # 提取fork数
            forks_element = repo_element.find('a', href=re.compile(r'/forks$'))
            forks = 0
            if forks_element:
                forks_text = forks_element.get_text().strip()
                forks = self._parse_number(forks_text)
            
            # 提取今日星数增长
            today_stars_element = repo_element.find('span', class_='d-inline-block float-sm-right')
            today_stars = 0
            if today_stars_element:
                today_stars_text = today_stars_element.get_text().strip()
                today_stars = self._parse_number(today_stars_text.replace('stars today', '').strip())
            
            # 提取作者信息
            author_elements = repo_element.find_all('img', class_='avatar mb-1')
            authors = []
            for img in author_elements[:5]:  # 最多显示5个作者
                alt_text = img.get('alt', '')
                if alt_text and alt_text.startswith('@'):
                    authors.append(alt_text[1:])  # 去掉@符号
            
            repo_info = {
                'name': repo_name,
                'url': repo_url,
                'description': description,
                'language': language,
                'stars': stars,
                'forks': forks,
                'today_stars': today_stars,
                'authors': authors,
                'crawl_time': datetime.now().isoformat()
            }
            
            logger.debug(f"解析仓库信息: {repo_name}")
            return repo_info
            
        except Exception as e:
            logger.error(f"解析仓库信息时出错: {e}")
            return None
    
    def _parse_number(self, text: str) -> int:
        """
        解析数字字符串，支持k、m等单位
        
        Args:
            text: 数字字符串
            
        Returns:
            整数值
        """
        if not text:
            return 0
            
        text = text.lower().replace(',', '').strip()
        
        if 'k' in text:
            return int(float(text.replace('k', '')) * 1000)
        elif 'm' in text:
            return int(float(text.replace('m', '')) * 1000000)
        else:
            try:
                return int(text)
            except ValueError:
                return 0
    
    def crawl_trending_repositories(self, period: str = 'daily') -> List[Dict]:
        """
        爬取GitHub热门仓库
        
        Args:
            period: 时间周期 ('daily', 'weekly', 'monthly')
            
        Returns:
            仓库信息列表
        """
        url_map = {
            'daily': GITHUB_TRENDING_DAILY,
            'weekly': GITHUB_TRENDING_WEEKLY,
            'monthly': GITHUB_TRENDING_MONTHLY
        }
        
        url = url_map.get(period, GITHUB_TRENDING_DAILY)
        logger.info(f"开始爬取GitHub热门仓库 ({period})")
        
        html_content = self.fetch_page(url)
        if not html_content:
            logger.error("无法获取页面内容")
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找所有仓库条目
        repo_elements = soup.find_all('article', class_='Box-row')
        
        repositories = []
        for i, repo_element in enumerate(repo_elements):
            if i >= MAX_REPOSITORIES:
                break
                
            repo_info = self.parse_repository_info(repo_element)
            if repo_info:
                repositories.append(repo_info)
        
        logger.info(f"成功爬取 {len(repositories)} 个仓库信息")
        return repositories


if __name__ == "__main__":
    spider = GitHubSpider()
    repos = spider.crawl_trending_repositories()
    for repo in repos:
        print(f"{repo['name']} - {repo['stars']} stars - {repo['language']}")
