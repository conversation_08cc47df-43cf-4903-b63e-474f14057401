"""
GitHub热门仓库爬虫配置文件
"""

import os
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# GitHub相关配置
GITHUB_TRENDING_URL = "https://github.com/trending"
GITHUB_TRENDING_DAILY = "https://github.com/trending?since=daily"
GITHUB_TRENDING_WEEKLY = "https://github.com/trending?since=weekly"
GITHUB_TRENDING_MONTHLY = "https://github.com/trending?since=monthly"

# 请求配置
REQUEST_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 5  # 秒

# 代理配置（如果需要）
# 如果您有代理服务器，请取消注释并配置
# PROXIES = {
#     'http': 'http://127.0.0.1:7890',
#     'https': 'http://127.0.0.1:7890'
# }
PROXIES = None  # 不使用代理

# 定时任务配置
SCHEDULE_TIME = "12:00"  # 每天12点执行

# 文件路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, "data")
REPORTS_DIR = os.path.join(BASE_DIR, "reports")
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# 确保目录存在
for directory in [DATA_DIR, REPORTS_DIR, LOGS_DIR]:
    os.makedirs(directory, exist_ok=True)

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = os.path.join(LOGS_DIR, f"github_spider_{datetime.now().strftime('%Y%m')}.log")

# 数据文件配置
DATA_FILE_TEMPLATE = os.path.join(DATA_DIR, "trending_{date}.json")
REPORT_FILE_TEMPLATE = os.path.join(REPORTS_DIR, "GitHub热门项目_{date}.md")

# 爬取配置
MAX_REPOSITORIES = 25  # 每次爬取的最大仓库数量
INCLUDE_LANGUAGES = True  # 是否包含编程语言信息
INCLUDE_STARS = True  # 是否包含星数信息
INCLUDE_FORKS = True  # 是否包含fork数信息
INCLUDE_AUTHORS = True  # 是否包含作者信息

# Markdown报告配置
REPORT_TITLE = "GitHub 今日热门项目"
REPORT_SUBTITLE = "每日精选优质开源项目"
INCLUDE_STATISTICS = True  # 是否包含统计信息
INCLUDE_LANGUAGE_STATS = True  # 是否包含语言统计

# ==================== AI翻译配置 ====================
# DeepSeek API配置 - 从环境变量获取（安全最佳实践）
DEEPSEEK_API_KEY = os.environ.get("DEEPSEEK_API_KEY")

# 是否启用AI翻译
ENABLE_AI_TRANSLATION = True

# 翻译配置
TRANSLATION_TIMEOUT = 30  # 翻译超时时间（秒）
TRANSLATION_RETRY_COUNT = 2  # 翻译重试次数
TRANSLATION_DELAY = 0.5  # 翻译请求间隔（秒）

# ==================== 高级配置 ====================
# 数据保留天数（自动清理）
DATA_RETENTION_DAYS = 30

# 代理配置（如果需要）
PROXY_CONFIG = None
