"""
网络连接测试脚本
"""

import requests
import time
from config import HEADERS, REQUEST_TIMEOUT, PROXIES

def test_github_connection():
    """测试GitHub连接"""
    print("🔍 测试GitHub连接...")
    
    # 测试URL列表
    test_urls = [
        "https://github.com",
        "https://github.com/trending",
        "https://github.com/trending?since=daily"
    ]
    
    session = requests.Session()
    session.headers.update(HEADERS)
    
    if PROXIES:
        session.proxies.update(PROXIES)
        print(f"📡 使用代理: {PROXIES}")
    else:
        print("🌐 直连模式")
    
    for url in test_urls:
        print(f"\n🔗 测试: {url}")
        try:
            start_time = time.time()
            response = session.get(url, timeout=REQUEST_TIMEOUT)
            end_time = time.time()
            
            print(f"✅ 成功! 状态码: {response.status_code}")
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
            print(f"📦 内容长度: {len(response.text)} 字符")
            
        except requests.exceptions.Timeout:
            print(f"❌ 超时! (>{REQUEST_TIMEOUT}秒)")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_alternative_sources():
    """测试替代数据源"""
    print("\n🔄 测试替代数据源...")
    
    # GitHub API (通常更稳定)
    api_url = "https://api.github.com/search/repositories?q=stars:>1&sort=stars&order=desc&per_page=10"
    
    try:
        print(f"🔗 测试GitHub API: {api_url}")
        response = requests.get(api_url, timeout=REQUEST_TIMEOUT)
        print(f"✅ GitHub API可用! 状态码: {response.status_code}")
        
        data = response.json()
        print(f"📊 返回了 {len(data.get('items', []))} 个仓库")
        
    except Exception as e:
        print(f"❌ GitHub API不可用: {e}")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("1. 🔧 配置代理服务器")
    print("   - 如果您有代理，请在config.py中配置PROXIES")
    print("   - 常见代理端口: 7890, 1080, 8080")
    print()
    print("2. 🌐 使用VPN")
    print("   - 连接到海外VPN节点")
    print("   - 确保VPN稳定且速度良好")
    print()
    print("3. 📱 使用手机热点")
    print("   - 某些移动网络可能访问GitHub更稳定")
    print()
    print("4. ⏰ 错峰访问")
    print("   - 避开网络高峰期（如晚上8-11点）")
    print("   - 建议在早上或深夜运行")
    print()
    print("5. 🔄 使用GitHub API")
    print("   - API通常比网页版更稳定")
    print("   - 可以考虑切换到API模式")

if __name__ == "__main__":
    print("🚀 GitHub网络连接测试")
    print("=" * 50)
    
    test_github_connection()
    test_alternative_sources()
    suggest_solutions()
    
    print("\n" + "=" * 50)
    print("测试完成!")
