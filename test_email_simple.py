"""
简单邮件测试 - 恢复到5:25成功版本的逻辑
"""

from email_sender import EmailSender
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_email():
    """测试邮件发送"""
    print("📧 测试企业微信邮箱发送")
    print("=" * 40)
    
    # 显示配置
    print(f"SMTP服务器: {os.environ.get('EMAIL_SMTP_SERVER')}")
    print(f"SMTP端口: {os.environ.get('EMAIL_SMTP_PORT')}")
    print(f"发送邮箱: {os.environ.get('EMAIL_FROM')}")
    print(f"接收邮箱: {os.environ.get('EMAIL_TO')}")
    
    # 创建邮件发送器
    email_sender = EmailSender()
    
    # 测试连接
    print("\n🔗 测试SMTP连接...")
    if email_sender.test_connection():
        print("✅ SMTP连接成功")
    else:
        print("❌ SMTP连接失败")
        return False
    
    # 检查是否有报告文件
    report_file = "reports/GitHub热门项目_2025-06-30.md"
    if os.path.exists(report_file):
        print(f"\n📄 发送报告: {report_file}")
        success = email_sender.send_report(report_file)
        if success:
            print("✅ 邮件发送成功！")
        else:
            print("❌ 邮件发送失败")
        return success
    else:
        print(f"\n❌ 报告文件不存在: {report_file}")
        return False

if __name__ == "__main__":
    test_email()
