"""
基础邮件测试 - 最简单的邮件发送测试
"""

import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_basic_email():
    """基础邮件发送测试"""
    print("📧 基础邮件发送测试")
    print("=" * 30)
    
    # 获取配置
    smtp_server = os.environ.get("EMAIL_SMTP_SERVER")
    smtp_port = int(os.environ.get("EMAIL_SMTP_PORT"))
    from_email = os.environ.get("EMAIL_FROM")
    password = os.environ.get("EMAIL_PASSWORD")
    to_email = os.environ.get("EMAIL_TO")
    
    print(f"SMTP服务器: {smtp_server}:{smtp_port}")
    print(f"发送邮箱: {from_email}")
    print(f"接收邮箱: {to_email}")
    
    try:
        # 创建邮件
        msg = MIMEMultipart()
        msg['From'] = from_email
        msg['To'] = to_email
        msg['Subject'] = Header("测试邮件 - 编码修复", 'utf-8')
        
        # 邮件正文
        body = "这是一个测试邮件，用于验证编码修复是否成功。\n\n包含中文字符：你好世界！"
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        print("📤 连接SMTP服务器...")
        # 连接并发送
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_email, password)
        
        print("📤 发送邮件...")
        text = msg.as_string()
        server.sendmail(from_email, to_email, text)
        server.quit()
        
        print("✅ 邮件发送成功！")
        return True
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        return False

if __name__ == "__main__":
    test_basic_email()
