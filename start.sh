#!/bin/bash

echo ""
echo "========================================"
echo "    GitHub热门仓库爬虫启动脚本"
echo "========================================"
echo ""
echo "请选择运行模式:"
echo "1. 定时任务模式 (每天12点自动执行)"
echo "2. 手动执行 - 今日热门"
echo "3. 手动执行 - 本周热门"
echo "4. 手动执行 - 本月热门"
echo "5. 查看统计信息"
echo "6. 退出"
echo ""

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo "启动定时任务模式..."
        python3 main.py --mode schedule
        ;;
    2)
        echo "执行今日热门爬取..."
        python3 main.py --mode manual --period daily
        ;;
    3)
        echo "执行本周热门爬取..."
        python3 main.py --mode manual --period weekly
        ;;
    4)
        echo "执行本月热门爬取..."
        python3 main.py --mode manual --period monthly
        ;;
    5)
        echo "显示统计信息..."
        python3 main.py --mode stats --days 7
        ;;
    6)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效选择，请重新运行脚本"
        ;;
esac

echo ""
read -p "按回车键继续..."
