"""
快速邮件测试
"""

import smtplib
import socket
from dotenv import load_dotenv
import os

load_dotenv()

def quick_test():
    host = "smtp.exmail.qq.com"
    email = os.environ.get("EMAIL_FROM")
    password = os.environ.get("EMAIL_PASSWORD")
    
    print(f"测试邮箱: {email}")
    print(f"SMTP服务器: {host}")
    
    # 测试端口465 (SSL)
    print("\n测试SSL端口465...")
    try:
        server = smtplib.SMTP_SSL(host, 465, timeout=10)
        server.login(email, password)
        server.quit()
        print("✅ SSL端口465连接成功！")
        print("建议使用: EMAIL_SMTP_PORT=\"465\"")
        return True
    except Exception as e:
        print(f"❌ SSL端口465失败: {e}")
    
    # 测试端口587 (TLS)
    print("\n测试TLS端口587...")
    try:
        server = smtplib.SMTP(host, 587, timeout=10)
        server.starttls()
        server.login(email, password)
        server.quit()
        print("✅ TLS端口587连接成功！")
        print("建议使用: EMAIL_SMTP_PORT=\"587\"")
        return True
    except Exception as e:
        print(f"❌ TLS端口587失败: {e}")
    
    print("\n❌ 所有端口都无法连接")
    print("可能原因:")
    print("1. 网络防火墙阻止")
    print("2. 企业网络限制")
    print("3. 邮箱密码错误")
    
    return False

if __name__ == "__main__":
    quick_test()
