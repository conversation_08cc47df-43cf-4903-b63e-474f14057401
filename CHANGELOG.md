# 更新日志

所有重要的项目变更都会记录在这个文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [2.0.0] - 2025-06-30

### 🎉 新增功能
- **智能报告生成**: 全新设计的Markdown报告格式
- **数据洞察面板**: 核心指标、榜单展示、技术栈分析
- **智能标签系统**: 自动生成技术标签和项目亮点
- **中文本土化**: 智能翻译项目描述，优化中文阅读体验
- **开发者工具箱**: 精选学习资源和实用工具推荐
- **趋势观察**: 增长分类统计和AI/ML项目识别
- **可视化进度条**: 编程语言分布的图形化展示

### 🔧 改进优化
- **链接格式修复**: 解决项目名称换行导致的链接显示问题
- **数据清洗**: 优化项目名称和描述的格式处理
- **报告结构**: 重新设计报告布局，提升可读性
- **统计算法**: 改进数据统计和分析逻辑

### 🌟 界面优化
- **表格展示**: 使用表格形式展示核心数据
- **图标系统**: 丰富的emoji图标提升视觉效果
- **分区布局**: 清晰的内容分区和导航结构
- **响应式设计**: 适配不同设备的阅读体验

### 📚 文档更新
- **README优化**: 详细的功能介绍和使用说明
- **配置示例**: 提供完整的配置文件模板
- **启动脚本**: Windows和Linux/Mac的便捷启动脚本

## [1.0.0] - 2025-06-30

### 🎉 首次发布
- **基础爬虫**: GitHub Trending页面数据抓取
- **定时任务**: 每日12点自动执行爬虫
- **数据存储**: JSON格式的历史数据保存
- **报告生成**: 基础的Markdown格式报告
- **日志系统**: 完整的运行日志记录
- **配置管理**: 灵活的配置文件系统

### 🔧 核心功能
- **多周期支持**: 支持日/周/月不同时间维度
- **错误处理**: 完善的异常处理和重试机制
- **数据清理**: 自动清理过期数据
- **统计分析**: 基础的数据统计功能

---

## 版本说明

### 版本号规则
- **主版本号**: 重大功能更新或架构变更
- **次版本号**: 新功能添加或重要改进
- **修订版本号**: Bug修复和小幅优化

### 更新类型
- 🎉 **新增**: 新功能或新特性
- 🔧 **改进**: 现有功能的优化和改进
- 🐛 **修复**: Bug修复和问题解决
- 📚 **文档**: 文档更新和说明改进
- 🌟 **界面**: 用户界面和体验优化
- ⚡ **性能**: 性能优化和效率提升

### 兼容性说明
- **2.0.0**: 报告格式有重大变更，建议重新生成报告
- **1.0.0**: 初始版本，建立基础功能架构

---

*更多详细信息请查看 [GitHub Releases](https://github.com/your-repo/releases)*
