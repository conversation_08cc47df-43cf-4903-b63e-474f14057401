"""
SMTP连接诊断工具
测试不同的SMTP配置组合
"""

import socket
import smtplib
import ssl
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

def test_port_connectivity(host, port, timeout=10):
    """测试端口连通性"""
    try:
        sock = socket.create_connection((host, port), timeout)
        sock.close()
        return True
    except Exception as e:
        print(f"❌ 端口 {port} 连接失败: {e}")
        return False

def test_smtp_configs():
    """测试多种SMTP配置"""
    host = "smtp.exmail.qq.com"
    email = os.environ.get("EMAIL_FROM")
    password = os.environ.get("EMAIL_PASSWORD")
    
    if not email or not password:
        print("❌ 请先配置EMAIL_FROM和EMAIL_PASSWORD")
        return
    
    print(f"📧 测试邮箱: {email}")
    print(f"🔧 SMTP服务器: {host}")
    print("=" * 50)
    
    # 测试配置列表
    configs = [
        {"port": 465, "ssl": True, "name": "SSL"},
        {"port": 587, "ssl": False, "name": "TLS"},
        {"port": 25, "ssl": False, "name": "普通SMTP"},
    ]
    
    for config in configs:
        port = config["port"]
        use_ssl = config["ssl"]
        name = config["name"]
        
        print(f"\n🔍 测试 {name} (端口 {port})")
        print("-" * 30)
        
        # 1. 测试端口连通性
        print(f"1. 测试端口连通性...")
        if not test_port_connectivity(host, port):
            print(f"   ❌ 端口 {port} 无法连接")
            continue
        else:
            print(f"   ✅ 端口 {port} 连接正常")
        
        # 2. 测试SMTP连接
        print(f"2. 测试SMTP连接...")
        try:
            if use_ssl:
                # SSL连接
                server = smtplib.SMTP_SSL(host, port, timeout=10)
                print(f"   ✅ SSL连接成功")
            else:
                # 普通连接 + TLS
                server = smtplib.SMTP(host, port, timeout=10)
                if port == 587:
                    server.starttls()
                    print(f"   ✅ TLS加密启用成功")
                else:
                    print(f"   ✅ 普通SMTP连接成功")
            
            # 3. 测试登录
            print(f"3. 测试邮箱登录...")
            server.login(email, password)
            print(f"   ✅ 邮箱登录成功")
            
            server.quit()
            print(f"🎉 {name} 配置测试成功！")
            
            # 更新.env文件建议
            print(f"\n💡 建议配置:")
            print(f"   EMAIL_SMTP_PORT=\"{port}\"")
            
            return True
            
        except Exception as e:
            print(f"   ❌ {name} 测试失败: {e}")
            continue
    
    print("\n❌ 所有配置都测试失败")
    print("\n🔧 可能的解决方案:")
    print("1. 检查网络防火墙设置")
    print("2. 尝试使用VPN或代理")
    print("3. 联系网络管理员开放SMTP端口")
    print("4. 确认企业微信邮箱SMTP服务已启用")
    
    return False

def test_network_basic():
    """测试基本网络连接"""
    print("🌐 测试基本网络连接")
    print("=" * 30)
    
    test_hosts = [
        ("smtp.exmail.qq.com", "企业微信SMTP"),
        ("smtp.qq.com", "QQ邮箱SMTP"),
        ("smtp.gmail.com", "Gmail SMTP"),
        ("www.baidu.com", "百度"),
    ]
    
    for host, name in test_hosts:
        try:
            socket.gethostbyname(host)
            print(f"✅ {name} ({host}) - DNS解析正常")
        except Exception as e:
            print(f"❌ {name} ({host}) - DNS解析失败: {e}")

def main():
    """主函数"""
    print("🔧 SMTP连接诊断工具")
    print("=" * 50)
    
    # 1. 测试基本网络
    test_network_basic()
    
    print("\n")
    
    # 2. 测试SMTP配置
    test_smtp_configs()

if __name__ == "__main__":
    main()
