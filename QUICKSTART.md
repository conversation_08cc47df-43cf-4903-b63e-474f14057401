# 🚀 快速开始指南

欢迎使用GitHub热门仓库爬虫！这个指南将帮助你在5分钟内开始使用。

## 📋 前置要求

- Python 3.7+ 
- 网络连接（访问GitHub）
- 约50MB磁盘空间

## ⚡ 一键启动

### Windows用户
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 双击启动
start.bat
```

### Linux/Mac用户
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 添加执行权限并启动
chmod +x start.sh
./start.sh
```

## 🎯 快速体验

### 立即生成一份报告
```bash
python main.py --mode manual --period daily
```

执行后会在当前目录生成类似 `GitHub热门项目_daily_20250630_135121.md` 的报告文件。

### 查看生成的报告
报告包含以下精彩内容：

#### 📊 数据洞察面板
```
📦 项目总览          16 个精选项目
⭐ 社区认可      380.4K 总星标数
🔥 今日热度        4.5K 新增关注
📈 平均质量       23.8K 平均星标
```

#### 🏆 今日榜单
- 🥇 **人气王**: 最受欢迎的项目
- 🚀 **增长王**: 今日增长最快的项目

#### 💻 技术栈分析
```
🐍 Python
   ██████░░░░░░░░░░░░░░ 5 项目 (31.2%)

🔷 TypeScript  
   █████░░░░░░░░░░░░░░░ 4 项目 (25.0%)
```

#### 🚀 热门项目详情
每个项目包含：
- 📝 **项目简介**: 中文翻译的描述
- 🏷️ **技术标签**: 自动生成的特性标签
- 💻 **技术信息**: 语言、星数、Fork数
- ✨ **项目亮点**: 基于数据的特色标识

## 🕐 设置定时任务

### 启动自动爬虫
```bash
python main.py --mode schedule
```

程序会：
- 每天12:00自动执行
- 生成当日报告
- 保存历史数据
- 记录运行日志

### 自定义执行时间
编辑 `config.py` 文件：
```python
SCHEDULE_TIME = "09:00"  # 改为上午9点执行
```

## 📊 查看统计信息

```bash
# 查看最近7天统计
python main.py --mode stats --days 7

# 查看最近30天统计  
python main.py --mode stats --days 30
```

## 🔧 常用配置

### 修改爬取数量
```python
# config.py
MAX_REPOSITORIES = 50  # 改为爬取50个项目
```

### 自定义报告标题
```python
# config.py
REPORT_TITLE = "我的GitHub热门项目日报"
```

### 启用调试模式
```python
# config.py
LOG_LEVEL = "DEBUG"  # 显示详细日志
```

## 📁 文件说明

生成的文件位置：
```
📁 data/           # 原始数据存储
  └── trending_2025-06-30.json
📁 reports/        # 正式报告存储  
  └── GitHub热门项目_2025-06-30.md
📁 logs/           # 运行日志
  └── github_spider_202506.log
```

## 🆘 常见问题

### Q: 网络连接失败怎么办？
A: 检查网络连接，确认可以访问GitHub。如需代理，在config.py中配置PROXY_CONFIG。

### Q: 生成的报告是空的？
A: 可能是GitHub页面结构变化，查看日志文件获取详细错误信息。

### Q: 如何停止定时任务？
A: 在运行窗口按 `Ctrl+C` 即可停止程序。

### Q: 可以爬取其他语言的项目吗？
A: 可以！修改config.py中的URL配置，例如：
```python
GITHUB_TRENDING_URL = "https://github.com/trending/python"  # 只爬取Python项目
```

## 🎉 下一步

1. **个性化配置**: 根据需要修改config.py
2. **定时运行**: 设置系统定时任务或服务
3. **数据分析**: 利用保存的历史数据进行趋势分析
4. **分享报告**: 将生成的Markdown报告分享给团队

## 📞 获取帮助

- 📖 查看完整文档: [README.md](README.md)
- 🐛 报告问题: 提交GitHub Issue
- 💡 功能建议: 欢迎提交Pull Request

---

**🎯 现在就开始探索GitHub的热门项目吧！**
