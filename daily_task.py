"""
每日定时任务脚本
专门用于Code Runner定时执行
"""

import os
import sys
import logging
from datetime import datetime
from main import GitHubTrendingCrawler

# 设置工作目录
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 配置日志
log_file = f"logs/daily_task_{datetime.now().strftime('%Y%m%d')}.log"
os.makedirs("logs", exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def run_daily_task():
    """执行每日任务"""
    logger.info("🚀 开始执行每日GitHub热门项目爬取任务")
    logger.info(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📁 工作目录: {os.getcwd()}")
    
    try:
        # 创建爬虫实例
        crawler = GitHubTrendingCrawler()
        
        # 执行任务
        success = crawler.run_daily_task()
        
        if success:
            logger.info("✅ 每日任务执行成功")
            print("✅ 任务完成！GitHub热门项目报告已生成并发送到邮箱")
        else:
            logger.error("❌ 每日任务执行失败")
            print("❌ 任务失败！请检查日志文件获取详细信息")
            
        return success
        
    except Exception as e:
        logger.error(f"❌ 执行任务时发生异常: {e}")
        print(f"❌ 执行失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🤖 GitHub热门项目自动爬虫")
    print("📧 报告将自动发送到: <EMAIL>")
    print("=" * 60)
    
    success = run_daily_task()
    
    print("=" * 60)
    if success:
        print("🎉 任务执行完成！")
    else:
        print("⚠️  任务执行遇到问题，请检查配置和网络连接")
    print("=" * 60)
