"""
项目清理脚本
清理Python缓存文件和其他临时文件
"""

import os
import shutil
import glob

def clean_pycache():
    """清理Python缓存文件"""
    print("🧹 清理Python缓存文件...")
    
    # 查找所有__pycache__目录
    pycache_dirs = []
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_dirs.append(os.path.join(root, '__pycache__'))
    
    # 删除__pycache__目录
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            print(f"✅ 删除: {pycache_dir}")
        except Exception as e:
            print(f"❌ 删除失败 {pycache_dir}: {e}")
    
    # 查找并删除.pyc文件
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"✅ 删除: {pyc_file}")
        except Exception as e:
            print(f"❌ 删除失败 {pyc_file}: {e}")

def clean_logs():
    """清理旧日志文件（可选）"""
    print("\n📋 检查日志文件...")
    
    if os.path.exists('logs'):
        log_files = os.listdir('logs')
        if log_files:
            print(f"📁 发现 {len(log_files)} 个日志文件")
            print("💡 提示: 如需清理日志，请手动删除 logs/ 目录中的文件")
        else:
            print("📁 日志目录为空")

def show_current_structure():
    """显示当前项目结构"""
    print("\n📂 当前项目结构:")
    print("=" * 40)
    
    # 核心文件
    core_files = [
        'main.py',
        'config.py', 
        'github_spider.py',
        'translator.py',
        'report_generator.py',
        'data_manager.py'
    ]
    
    print("🔧 核心文件:")
    for file in core_files:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"  {status} {file}")
    
    # 配置文件
    config_files = [
        '.env',
        'requirements.txt',
        '.gitignore'
    ]
    
    print("\n⚙️ 配置文件:")
    for file in config_files:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"  {status} {file}")
    
    # 测试文件
    test_files = [
        'test_config.py',
        'test_direct_api.py',
        'test_network.py'
    ]
    
    print("\n🧪 测试文件:")
    for file in test_files:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"  {status} {file}")
    
    # 文档文件
    doc_files = [
        'README.md',
        'DEEPSEEK_API_UPDATE.md',
        'NETWORK_SOLUTIONS.md'
    ]
    
    print("\n📚 文档文件:")
    for file in doc_files:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"  {status} {file}")

if __name__ == "__main__":
    print("🚀 项目清理工具")
    print("=" * 40)
    
    clean_pycache()
    clean_logs()
    show_current_structure()
    
    print("\n" + "=" * 40)
    print("✨ 清理完成!")
    print("\n💡 建议:")
    print("1. 运行 'python test_config.py' 验证配置")
    print("2. 运行 'python test_direct_api.py' 测试API")
    print("3. 如果不再需要备用爬虫，可删除 'github_api_spider.py'")
