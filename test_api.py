"""
API测试模块 - 测试DeepSeek翻译API的连接和功能
功能：API连接测试、翻译功能测试、错误诊断
"""

import os
from dotenv import load_dotenv
from translator import ProjectTranslator

# 加载环境变量
load_dotenv()

def test_deepseek_api():
    """测试DeepSeek API连接和翻译功能"""
    print("🔧 测试DeepSeek API")
    print("=" * 40)
    
    # 获取API密钥
    api_key = os.environ.get("DEEPSEEK_API_KEY")
    if not api_key:
        print("❌ 未找到DEEPSEEK_API_KEY环境变量")
        return False
    
    print(f"API密钥: {api_key[:10]}...{api_key[-10:]}")
    
    try:
        # 创建翻译器
        translator = ProjectTranslator(api_key)
        print("✅ 翻译器创建成功")
        
        # 测试翻译
        test_text = "A modern web framework for Python"
        print(f"\n🔄 测试翻译: {test_text}")
        
        result = translator.translate_description(test_text)
        if result:
            print(f"✅ 翻译成功: {result}")
            return True
        else:
            print("❌ 翻译失败")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    test_deepseek_api()
