"""
简单测试脚本 - 诊断导入和基本功能问题
"""

print("🔧 开始诊断...")

try:
    print("1. 测试基本导入...")
    import os
    import sys
    print("✅ 基本模块导入成功")
    
    print("2. 测试配置模块...")
    import config
    print("✅ 配置模块导入成功")
    
    print("3. 测试邮件发送模块...")
    from email_sender import EmailSender
    print("✅ 邮件发送模块导入成功")
    
    print("4. 测试邮件发送器初始化...")
    email_sender = EmailSender()
    print("✅ 邮件发送器初始化成功")
    
    print("5. 测试SMTP连接...")
    if email_sender.test_connection():
        print("✅ SMTP连接测试成功")
    else:
        print("❌ SMTP连接测试失败")
    
    print("6. 测试主程序模块...")
    from main import GitHubTrendingBot
    print("✅ 主程序模块导入成功")
    
    print("7. 测试机器人初始化...")
    bot = GitHubTrendingBot()
    print("✅ 机器人初始化成功")
    
    print("\n🎉 所有基本功能测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
