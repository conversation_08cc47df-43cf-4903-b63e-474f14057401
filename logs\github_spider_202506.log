2025-06-30 12:03:02,847 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 12:03:02,847 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 12:03:02,847 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 12:03:04,225 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 12:03:04,322 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 12:03:04,323 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 12:03:04,323 - report_generator - INFO - Markdown报告生成完成
2025-06-30 12:03:04,324 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_120304.md
2025-06-30 12:03:16,350 - __main__ - INFO - 获取最近 1 天的统计信息
2025-06-30 12:03:16,350 - data_manager - WARNING - 数据文件不存在: D:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:40,291 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 13:42:51,037 - __main__ - INFO - ==================================================
2025-06-30 13:42:51,037 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 13:42:51,037 - __main__ - INFO - ==================================================
2025-06-30 13:42:51,037 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 13:42:51,037 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:42:51,038 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:42:52,600 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:42:52,688 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:42:52,688 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 13:42:52,689 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 13:42:52,690 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:52,690 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:52,690 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 13:42:52,690 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:42:52,691 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:42:52,691 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 13:42:52,692 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 13:42:52,692 - __main__ - INFO - 步骤 4: 清理旧数据
2025-06-30 13:42:52,694 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 13:42:52,694 - __main__ - INFO - ==================================================
2025-06-30 13:42:52,694 - __main__ - INFO - 每日任务执行完成
2025-06-30 13:42:52,695 - __main__ - INFO - ==================================================
2025-06-30 13:49:31,646 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:49:31,647 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:49:31,647 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:49:33,184 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:49:33,266 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:49:33,267 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:49:33,271 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:49:33,272 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_134933.md
2025-06-30 13:50:21,440 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:50:21,440 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:50:21,441 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:50:51,641 - github_spider - WARNING - 获取页面失败 (尝试 1/3): HTTPSConnectionPool(host='github.com', port=443): Read timed out. (read timeout=30)
2025-06-30 13:50:56,642 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 13:50:58,427 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:50:58,508 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:50:58,509 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:50:58,510 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:50:58,511 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135058.md
2025-06-30 13:51:06,609 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:51:06,609 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:51:06,609 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:51:09,357 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:51:09,450 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:51:09,451 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:51:09,452 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:51:09,452 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135109.md
2025-06-30 13:51:20,076 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:51:20,077 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:51:20,077 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:51:21,637 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:51:21,717 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:51:21,717 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:51:21,718 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:51:21,719 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135121.md
2025-06-30 14:28:30,707 - report_generator - ERROR - AI翻译器初始化失败: Client.__init__() got an unexpected keyword argument 'proxies'
2025-06-30 14:28:30,708 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:28:30,708 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:28:30,708 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:28:32,328 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:28:32,418 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:28:32,418 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:28:32,420 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:28:32,421 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_142832.md
2025-06-30 14:31:49,308 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:31:49,308 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:31:49,308 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:32:19,522 - github_spider - WARNING - 获取页面失败 (尝试 1/3): HTTPSConnectionPool(host='github.com', port=443): Read timed out. (read timeout=30)
2025-06-30 14:32:24,523 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 14:32:46,405 - github_spider - WARNING - 获取页面失败 (尝试 2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 14:32:51,405 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 3/3)
2025-06-30 14:32:52,853 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:32:52,943 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:32:52,944 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:32:52,945 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:32:52,946 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_143252.md
2025-06-30 14:37:20,731 - report_generator - INFO - AI翻译器初始化成功
2025-06-30 14:37:20,732 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:37:20,732 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:37:20,732 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:37:40,161 - github_spider - WARNING - 获取页面失败 (尝试 1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 14:37:45,161 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 14:37:46,677 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:37:46,773 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:37:46,773 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:37:46,774 - translator - INFO - 正在翻译项目描述: Building a modern alternative to Salesforce, power...
2025-06-30 14:37:55,270 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:37:55,271 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:37:55,271 - translator - INFO - 正在翻译项目描述: 2D vector & raster editor that melds traditional l...
2025-06-30 14:38:03,351 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:03,352 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:03,352 - translator - INFO - 正在翻译项目描述: 21 Lessons, Get Started Building with Generative A...
2025-06-30 14:38:11,430 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:11,430 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:11,431 - translator - INFO - 正在翻译项目描述: FULL v0, Cursor, Manus, Same.dev, Lovable, Devin, ...
2025-06-30 14:38:19,536 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:19,536 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:19,537 - translator - INFO - 正在翻译项目描述: All the open source AI Agents hosted on the oTToma...
2025-06-30 14:38:27,626 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:27,626 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:27,627 - translator - INFO - 正在翻译项目描述: An LLM-powered knowledge curation system that rese...
2025-06-30 14:38:35,716 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:35,716 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:35,717 - translator - INFO - 正在翻译项目描述: A visual no-code theme editor for shadcn/ui compon...
2025-06-30 14:38:43,795 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:43,795 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:43,795 - translator - INFO - 正在翻译项目描述: 🔥 Turn entire websites into LLM-ready markdown or ...
2025-06-30 14:38:51,896 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:51,897 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:51,897 - translator - INFO - 正在翻译项目描述: Perplexica is an AI-powered search engine. It is a...
2025-06-30 14:38:59,970 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:59,971 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:59,971 - translator - INFO - 正在翻译项目描述: BookLore is a web app for hosting, managing, and e...
2025-06-30 14:39:08,046 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:08,047 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:08,047 - translator - INFO - 正在翻译项目描述: A list of useful payloads and bypass for Web Appli...
2025-06-30 14:39:16,189 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:16,190 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:16,193 - translator - INFO - 正在翻译项目描述: A self hosted virtual browser that runs in docker ...
2025-06-30 14:39:24,296 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:24,297 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:24,297 - translator - INFO - 正在翻译项目描述: Supercharge Your LLM with the Fastest KV Cache Lay...
2025-06-30 14:39:32,386 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:32,387 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:32,387 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:39:32,388 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_143932.md
2025-06-30 14:42:54,199 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:42:54,199 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:42:54,199 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:43:10,859 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:43:10,949 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:43:10,949 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:43:10,950 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:43:10,951 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_144310.md
2025-06-30 14:44:17,634 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:44:17,634 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:44:17,634 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:44:19,119 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:44:19,204 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:44:19,204 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:44:19,206 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:44:19,207 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_144419.md
2025-06-30 15:21:05,383 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 15:21:09,720 - __main__ - INFO - ==================================================
2025-06-30 15:21:09,721 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 15:21:09,721 - __main__ - INFO - ==================================================
2025-06-30 15:21:09,721 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 15:21:09,721 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 15:21:09,721 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 15:21:29,164 - github_spider - WARNING - 获取页面失败 (尝试 1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 15:21:34,165 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 15:21:53,610 - github_spider - WARNING - 获取页面失败 (尝试 2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 15:21:58,611 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 3/3)
2025-06-30 15:22:19,670 - github_spider - WARNING - 获取页面失败 (尝试 3/3): HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /trending?since=daily (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001AFD9A59450>, 'Connection to github.com timed out. (connect timeout=30)'))
2025-06-30 15:22:19,671 - github_spider - ERROR - 获取页面最终失败: https://github.com/trending?since=daily
2025-06-30 15:22:19,671 - github_spider - ERROR - 无法获取页面内容
2025-06-30 15:22:19,671 - __main__ - ERROR - 未能获取到任何仓库数据，任务终止
2025-06-30 15:23:04,936 - __main__ - INFO - 用户中断，程序退出
2025-06-30 15:35:06,667 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 15:35:11,521 - __main__ - INFO - ==================================================
2025-06-30 15:35:11,522 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 15:35:11,522 - __main__ - INFO - ==================================================
2025-06-30 15:35:11,522 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 15:35:11,522 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 15:35:11,522 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 15:35:13,176 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 15:35:13,264 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 15:35:13,265 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 15:35:13,265 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 15:35:13,267 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 15:35:13,268 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 15:35:13,268 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 15:35:13,268 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 15:35:13,269 - report_generator - INFO - Markdown报告生成完成
2025-06-30 15:35:13,270 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 15:35:13,270 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 15:35:13,271 - __main__ - INFO - 步骤 4: 清理旧数据
2025-06-30 15:35:13,272 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 15:35:13,272 - __main__ - INFO - ==================================================
2025-06-30 15:35:13,273 - __main__ - INFO - 每日任务执行完成
2025-06-30 15:35:13,273 - __main__ - INFO - ==================================================
2025-06-30 15:38:06,873 - __main__ - INFO - 用户中断，程序退出
2025-06-30 16:10:55,733 - report_generator - INFO - AI翻译器初始化成功
2025-06-30 16:10:55,735 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 16:11:00,155 - __main__ - INFO - ==================================================
2025-06-30 16:11:00,156 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 16:11:00,156 - __main__ - INFO - ==================================================
2025-06-30 16:11:00,156 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 16:11:00,156 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 16:11:00,157 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 16:11:02,778 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 16:11:02,869 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 16:11:02,870 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 16:11:02,870 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 16:11:02,874 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 16:11:02,874 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 16:11:02,874 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 16:11:02,875 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 16:11:02,875 - translator - INFO - 正在翻译项目描述: Building a modern alternative to Salesforce, power...
2025-06-30 16:11:03,607 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:08,323 - translator - INFO - 翻译完成: 打造一个由社区驱动的现代化Salesforce替代方案。...
2025-06-30 16:11:08,323 - translator - INFO - 正在翻译项目描述: 2D vector & raster editor that melds traditional l...
2025-06-30 16:11:08,394 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:15,040 - translator - INFO - 翻译完成: Graphite 是一款 2D 矢量（vector）与栅格（raster）编辑器，它将传统图层（la...
2025-06-30 16:11:15,040 - translator - INFO - 正在翻译项目描述: 21 Lessons, Get Started Building with Generative A...
2025-06-30 16:11:15,107 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:23,047 - translator - INFO - 翻译完成: 《生成式AI新手入门教程》  
21节课程，带你从零开始构建生成式AI应用 🔗 https://mi...
2025-06-30 16:11:23,048 - translator - INFO - 正在翻译项目描述: FULL v0, Cursor, Manus, Same.dev, Lovable, Devin, ...
2025-06-30 16:11:23,124 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:31,899 - translator - INFO - 翻译完成: 完整版v0、Cursor、Manus、Same.dev、Lovable、Devin、Replit A...
2025-06-30 16:11:31,899 - translator - INFO - 正在翻译项目描述: All the open source AI Agents hosted on the oTToma...
2025-06-30 16:11:31,969 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:36,157 - translator - INFO - 翻译完成: ottoMator Live Agent Studio平台上托管的所有开源AI智能体（AI Agen...
2025-06-30 16:11:36,158 - translator - INFO - 正在翻译项目描述: An LLM-powered knowledge curation system that rese...
2025-06-30 16:11:36,228 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:41,651 - translator - INFO - 翻译完成: 一个基于大语言模型（LLM）的知识整理系统，能够自动研究指定主题并生成带引用来源的完整报告。...
2025-06-30 16:11:41,651 - translator - INFO - 正在翻译项目描述: A visual no-code theme editor for shadcn/ui compon...
2025-06-30 16:11:41,724 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:45,678 - translator - INFO - 翻译完成: 一款为 shadcn/ui 组件设计的可视化无代码（no-code）主题编辑器...
2025-06-30 16:11:45,679 - translator - INFO - 正在翻译项目描述: 🔥 Turn entire websites into LLM-ready markdown or ...
2025-06-30 16:11:45,747 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:51,086 - translator - INFO - 翻译完成: 🔥 将整个网站转化为适合大语言模型（LLM）处理的Markdown或结构化数据。通过单一API实现网...
2025-06-30 16:11:51,086 - translator - INFO - 正在翻译项目描述: Perplexica is an AI-powered search engine. It is a...
2025-06-30 16:11:51,155 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:11:55,579 - translator - INFO - 翻译完成: Perplexica 是一个基于人工智能(AI)的搜索引擎，作为 Perplexity AI 的开源...
2025-06-30 16:11:55,580 - translator - INFO - 正在翻译项目描述: BookLore is a web app for hosting, managing, and e...
2025-06-30 16:11:55,648 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:12:03,029 - translator - INFO - 翻译完成: BookLore 是一个用于托管、管理和探索书籍的网页应用，支持 PDF、电子书（eBook）、阅读...
2025-06-30 16:12:03,030 - translator - INFO - 正在翻译项目描述: A list of useful payloads and bypass for Web Appli...
2025-06-30 16:12:03,102 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:12:11,107 - translator - INFO - 翻译完成: 这是一个收集Web应用安全渗透测试（Pentest）和夺旗赛（CTF）中实用攻击载荷（Payload...
2025-06-30 16:12:11,108 - translator - INFO - 正在翻译项目描述: A self hosted virtual browser that runs in docker ...
2025-06-30 16:12:11,184 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:12:15,765 - translator - INFO - 翻译完成: 一个基于Docker自托管（self-hosted）的虚拟浏览器，使用WebRTC技术实现实时网页流...
2025-06-30 16:12:15,766 - translator - INFO - 正在翻译项目描述: Supercharge Your LLM with the Fastest KV Cache Lay...
2025-06-30 16:12:15,833 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:12:20,056 - translator - INFO - 翻译完成: 为你的大语言模型（LLM）加速——最快的键值缓存层（KV Cache）...
2025-06-30 16:12:20,056 - report_generator - INFO - Markdown报告生成完成
2025-06-30 16:12:20,057 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 16:12:20,058 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 16:12:20,058 - __main__ - INFO - 步骤 4: 清理旧数据
2025-06-30 16:12:20,060 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 16:12:20,061 - __main__ - INFO - ==================================================
2025-06-30 16:12:20,061 - __main__ - INFO - 每日任务执行完成
2025-06-30 16:12:20,061 - __main__ - INFO - ==================================================
2025-06-30 16:31:52,184 - report_generator - INFO - AI翻译器初始化成功
2025-06-30 16:31:52,184 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 16:31:55,399 - __main__ - INFO - ==================================================
2025-06-30 16:31:55,400 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 16:31:55,401 - __main__ - INFO - ==================================================
2025-06-30 16:31:55,401 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 16:31:55,401 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 16:31:55,402 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 16:32:14,870 - github_spider - WARNING - 获取页面失败 (尝试 1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 16:32:19,870 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 16:32:21,554 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 16:32:21,637 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 16:32:21,637 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 16:32:21,638 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 16:32:21,639 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 16:32:21,639 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 16:32:21,639 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 16:32:21,640 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 16:32:21,640 - translator - INFO - 正在翻译项目描述: Building a modern alternative to Salesforce, power...
2025-06-30 16:32:22,053 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:32:26,788 - translator - INFO - 翻译完成: 打造一个由社区驱动的现代化Salesforce替代方案。...
2025-06-30 16:32:26,788 - translator - INFO - 正在翻译项目描述: 2D vector & raster editor that melds traditional l...
2025-06-30 16:32:26,850 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:32:34,575 - translator - INFO - 翻译完成: Graphite 是一款 2D 矢量（vector）与栅格（raster）编辑器，它将传统图层（la...
2025-06-30 16:32:34,576 - translator - INFO - 正在翻译项目描述: 21 Lessons, Get Started Building with Generative A...
2025-06-30 16:32:34,655 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:32:44,477 - translator - INFO - 翻译完成: 《生成式AI新手入门教程》  
21节课程带你上手生成式AI（Generative AI）开发 🔗 ...
2025-06-30 16:32:44,477 - translator - INFO - 正在翻译项目描述: FULL v0, Cursor, Manus, Same.dev, Lovable, Devin, ...
2025-06-30 16:32:44,545 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:32:55,584 - translator - INFO - 翻译完成: 完整版v0、Cursor、Manus、Same.dev、Lovable、Devin、Replit A...
2025-06-30 16:32:55,585 - translator - INFO - 正在翻译项目描述: All the open source AI Agents hosted on the oTToma...
2025-06-30 16:32:55,674 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:32:59,870 - translator - INFO - 翻译完成: ottoMator Live Agent Studio平台上托管的所有开源AI智能体（AI Agen...
2025-06-30 16:32:59,871 - translator - INFO - 正在翻译项目描述: An LLM-powered knowledge curation system that rese...
2025-06-30 16:32:59,941 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:04,843 - translator - INFO - 翻译完成: 一个基于大语言模型（LLM）的知识整理系统，能够自动研究指定主题并生成带引用来源的完整报告。...
2025-06-30 16:33:04,844 - translator - INFO - 正在翻译项目描述: A visual no-code theme editor for shadcn/ui compon...
2025-06-30 16:33:04,915 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:15,392 - translator - INFO - 翻译完成: 一款为 shadcn/ui 组件设计的可视化无代码（no-code）主题编辑器...
2025-06-30 16:33:15,393 - translator - INFO - 正在翻译项目描述: 🔥 Turn entire websites into LLM-ready markdown or ...
2025-06-30 16:33:15,464 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:29,011 - translator - INFO - 翻译完成: 🔥 将整个网站转化为适合大语言模型（LLM）处理的Markdown或结构化数据。通过单一API实现网...
2025-06-30 16:33:29,012 - translator - INFO - 正在翻译项目描述: Perplexica is an AI-powered search engine. It is a...
2025-06-30 16:33:29,083 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:34,367 - translator - INFO - 翻译完成: Perplexica 是一个基于人工智能(AI)的搜索引擎，作为 Perplexity AI 的开源...
2025-06-30 16:33:34,368 - translator - INFO - 正在翻译项目描述: BookLore is a web app for hosting, managing, and e...
2025-06-30 16:33:34,434 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:40,423 - translator - INFO - 翻译完成: BookLore 是一个用于托管、管理和探索书籍的网页应用（web app），支持PDF、电子书（e...
2025-06-30 16:33:40,424 - translator - INFO - 正在翻译项目描述: A list of useful payloads and bypass for Web Appli...
2025-06-30 16:33:40,495 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:45,660 - translator - INFO - 翻译完成: 这是一个实用的Web应用安全（Web Application Security）和渗透测试/CTF（...
2025-06-30 16:33:45,661 - translator - INFO - 正在翻译项目描述: A self hosted virtual browser that runs in docker ...
2025-06-30 16:33:45,728 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:51,817 - translator - INFO - 翻译完成: 一个自托管（self-hosted）的虚拟浏览器，运行在Docker容器中，基于WebRTC技术实现...
2025-06-30 16:33:51,817 - translator - INFO - 正在翻译项目描述: Supercharge Your LLM with the Fastest KV Cache Lay...
2025-06-30 16:33:51,920 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 16:33:56,271 - translator - INFO - 翻译完成: 为你的大语言模型（LLM）加速——最快的键值缓存层（KV Cache）...
2025-06-30 16:33:56,272 - report_generator - INFO - Markdown报告生成完成
2025-06-30 16:33:56,272 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 16:33:56,273 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 16:33:56,273 - __main__ - INFO - 步骤 4: 发送邮件报告
2025-06-30 16:33:56,280 - email_sender - INFO - 已添加附件: GitHub热门项目_2025-06-30.md
2025-06-30 16:34:17,316 - email_sender - ERROR - 邮件发送失败: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
2025-06-30 16:34:17,317 - __main__ - WARNING - 邮件发送失败，请检查邮件配置
2025-06-30 16:34:17,317 - __main__ - INFO - 步骤 5: 清理旧数据
2025-06-30 16:34:17,319 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 16:34:17,319 - __main__ - INFO - ==================================================
2025-06-30 16:34:17,319 - __main__ - INFO - 每日任务执行完成
2025-06-30 16:34:17,319 - __main__ - INFO - ==================================================
2025-06-30 17:02:38,303 - __main__ - INFO - 用户中断，程序退出
2025-06-30 17:33:48,553 - report_generator - INFO - AI翻译器初始化成功
2025-06-30 17:33:48,553 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 17:33:51,866 - __main__ - INFO - ==================================================
2025-06-30 17:33:51,867 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 17:33:51,867 - __main__ - INFO - ==================================================
2025-06-30 17:33:51,868 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 17:33:51,868 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 17:33:51,868 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 17:33:53,265 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 17:33:53,362 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 17:33:53,363 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 17:33:53,363 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 17:33:53,364 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 17:33:53,364 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 17:33:53,364 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 17:33:53,365 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 17:33:53,365 - translator - INFO - 正在翻译项目描述: Building a modern alternative to Salesforce, power...
2025-06-30 17:33:53,865 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:33:58,094 - translator - INFO - 翻译完成: 构建一个由社区驱动的现代化Salesforce替代方案。...
2025-06-30 17:33:58,094 - translator - INFO - 正在翻译项目描述: 2D vector & raster editor that melds traditional l...
2025-06-30 17:33:58,178 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:03,900 - translator - INFO - 翻译完成: Graphite是一款2D矢量与栅格（raster）编辑器，它将传统图层（layers）和工具与现代...
2025-06-30 17:34:03,900 - translator - INFO - 正在翻译项目描述: 21 Lessons, Get Started Building with Generative A...
2025-06-30 17:34:03,969 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:13,180 - translator - INFO - 翻译完成: 《生成式AI入门教程》  
21节课程，带你从零开始构建生成式AI应用 🔗 https://micr...
2025-06-30 17:34:13,181 - translator - INFO - 正在翻译项目描述: FULL v0, Cursor, Manus, Same.dev, Lovable, Devin, ...
2025-06-30 17:34:13,244 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:19,887 - translator - INFO - 翻译完成: 完整版v0、Cursor、Manus、Same.dev、Lovable、Devin、Replit A...
2025-06-30 17:34:19,888 - translator - INFO - 正在翻译项目描述: All the open source AI Agents hosted on the oTToma...
2025-06-30 17:34:19,950 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:24,406 - translator - INFO - 翻译完成: ottoMATOR Live Agent 平台上托管的所有开源AI智能体（AI Agents）！...
2025-06-30 17:34:24,406 - translator - INFO - 正在翻译项目描述: An LLM-powered knowledge curation system that rese...
2025-06-30 17:34:24,474 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:35,506 - translator - INFO - 翻译完成: 一个基于大语言模型（LLM）的知识整理系统，能够自动研究指定主题并生成带引用来源的完整报告。...
2025-06-30 17:34:35,507 - translator - INFO - 正在翻译项目描述: A visual no-code theme editor for shadcn/ui compon...
2025-06-30 17:34:35,570 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:40,158 - translator - INFO - 翻译完成: 一款为shadcn/ui组件设计的可视化无代码（no-code）主题编辑器...
2025-06-30 17:34:40,158 - translator - INFO - 正在翻译项目描述: 🔥 Turn entire websites into LLM-ready markdown or ...
2025-06-30 17:34:40,222 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:45,959 - translator - INFO - 翻译完成: 🔥 将整个网站转化为可供大语言模型（LLM）直接使用的Markdown或结构化数据。通过单一API实...
2025-06-30 17:34:45,959 - translator - INFO - 正在翻译项目描述: Perplexica is an AI-powered search engine. It is a...
2025-06-30 17:34:46,039 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:51,185 - translator - INFO - 翻译完成: Perplexica 是一个基于人工智能(AI)的搜索引擎。作为 Perplexity AI 的开源...
2025-06-30 17:34:51,186 - translator - INFO - 正在翻译项目描述: BookLore is a web app for hosting, managing, and e...
2025-06-30 17:34:51,277 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:34:56,654 - translator - INFO - 翻译完成: BookLore 是一个用于托管、管理和探索书籍的网页应用，支持 PDF、电子书（eBook）、阅读...
2025-06-30 17:34:56,655 - translator - INFO - 正在翻译项目描述: A list of useful payloads and bypass for Web Appli...
2025-06-30 17:34:56,719 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:35:03,401 - translator - INFO - 翻译完成: 这是一个收集Web应用安全渗透测试（Pentest）和夺旗赛（CTF）中实用攻击载荷（Payload...
2025-06-30 17:35:03,402 - translator - INFO - 正在翻译项目描述: A self hosted virtual browser that runs in docker ...
2025-06-30 17:35:03,470 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:35:08,199 - translator - INFO - 翻译完成: 一个自托管（self-hosted）的虚拟浏览器，运行在Docker容器中，基于WebRTC技术实现...
2025-06-30 17:35:08,200 - translator - INFO - 正在翻译项目描述: Supercharge Your LLM with the Fastest KV Cache Lay...
2025-06-30 17:35:08,268 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-06-30 17:35:12,883 - translator - INFO - 翻译完成: 为你的大语言模型（LLM）加速——最快的键值缓存层（KV Cache）...
2025-06-30 17:35:12,884 - report_generator - INFO - Markdown报告生成完成
2025-06-30 17:35:12,884 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 17:35:12,885 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 17:35:12,885 - __main__ - INFO - 步骤 4: 发送邮件报告
2025-06-30 17:35:12,892 - email_sender - INFO - 已添加附件: GitHub热门项目_2025-06-30.md
2025-06-30 17:35:33,942 - email_sender - ERROR - 邮件发送失败: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
2025-06-30 17:35:33,942 - __main__ - WARNING - 邮件发送失败，请检查邮件配置
2025-06-30 17:35:33,943 - __main__ - INFO - 步骤 5: 清理旧数据
2025-06-30 17:35:33,945 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 17:35:33,946 - __main__ - INFO - ==================================================
2025-06-30 17:35:33,946 - __main__ - INFO - 每日任务执行完成
2025-06-30 17:35:33,946 - __main__ - INFO - ==================================================
2025-06-30 17:38:33,704 - __main__ - INFO - 用户中断，程序退出
