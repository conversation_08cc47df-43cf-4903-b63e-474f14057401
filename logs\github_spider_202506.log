2025-06-30 12:03:02,847 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 12:03:02,847 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 12:03:02,847 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 12:03:04,225 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 12:03:04,322 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 12:03:04,323 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 12:03:04,323 - report_generator - INFO - Markdown报告生成完成
2025-06-30 12:03:04,324 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_120304.md
2025-06-30 12:03:16,350 - __main__ - INFO - 获取最近 1 天的统计信息
2025-06-30 12:03:16,350 - data_manager - WARNING - 数据文件不存在: D:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:40,291 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 13:42:51,037 - __main__ - INFO - ==================================================
2025-06-30 13:42:51,037 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 13:42:51,037 - __main__ - INFO - ==================================================
2025-06-30 13:42:51,037 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 13:42:51,037 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:42:51,038 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:42:52,600 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:42:52,688 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:42:52,688 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 13:42:52,689 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 13:42:52,690 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:52,690 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:52,690 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 13:42:52,690 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:42:52,691 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:42:52,691 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 13:42:52,692 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 13:42:52,692 - __main__ - INFO - 步骤 4: 清理旧数据
2025-06-30 13:42:52,694 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 13:42:52,694 - __main__ - INFO - ==================================================
2025-06-30 13:42:52,694 - __main__ - INFO - 每日任务执行完成
2025-06-30 13:42:52,695 - __main__ - INFO - ==================================================
2025-06-30 13:49:31,646 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:49:31,647 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:49:31,647 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:49:33,184 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:49:33,266 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:49:33,267 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:49:33,271 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:49:33,272 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_134933.md
2025-06-30 13:50:21,440 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:50:21,440 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:50:21,441 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:50:51,641 - github_spider - WARNING - 获取页面失败 (尝试 1/3): HTTPSConnectionPool(host='github.com', port=443): Read timed out. (read timeout=30)
2025-06-30 13:50:56,642 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 13:50:58,427 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:50:58,508 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:50:58,509 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:50:58,510 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:50:58,511 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135058.md
2025-06-30 13:51:06,609 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:51:06,609 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:51:06,609 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:51:09,357 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:51:09,450 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:51:09,451 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:51:09,452 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:51:09,452 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135109.md
2025-06-30 13:51:20,076 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:51:20,077 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:51:20,077 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:51:21,637 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:51:21,717 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:51:21,717 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:51:21,718 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:51:21,719 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135121.md
2025-06-30 14:28:30,707 - report_generator - ERROR - AI翻译器初始化失败: Client.__init__() got an unexpected keyword argument 'proxies'
2025-06-30 14:28:30,708 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:28:30,708 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:28:30,708 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:28:32,328 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:28:32,418 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:28:32,418 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:28:32,420 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:28:32,421 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_142832.md
2025-06-30 14:31:49,308 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:31:49,308 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:31:49,308 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:32:19,522 - github_spider - WARNING - 获取页面失败 (尝试 1/3): HTTPSConnectionPool(host='github.com', port=443): Read timed out. (read timeout=30)
2025-06-30 14:32:24,523 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 14:32:46,405 - github_spider - WARNING - 获取页面失败 (尝试 2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 14:32:51,405 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 3/3)
2025-06-30 14:32:52,853 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:32:52,943 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:32:52,944 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:32:52,945 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:32:52,946 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_143252.md
2025-06-30 14:37:20,731 - report_generator - INFO - AI翻译器初始化成功
2025-06-30 14:37:20,732 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:37:20,732 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:37:20,732 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:37:40,161 - github_spider - WARNING - 获取页面失败 (尝试 1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 14:37:45,161 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 14:37:46,677 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:37:46,773 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:37:46,773 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:37:46,774 - translator - INFO - 正在翻译项目描述: Building a modern alternative to Salesforce, power...
2025-06-30 14:37:55,270 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:37:55,271 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:37:55,271 - translator - INFO - 正在翻译项目描述: 2D vector & raster editor that melds traditional l...
2025-06-30 14:38:03,351 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:03,352 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:03,352 - translator - INFO - 正在翻译项目描述: 21 Lessons, Get Started Building with Generative A...
2025-06-30 14:38:11,430 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:11,430 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:11,431 - translator - INFO - 正在翻译项目描述: FULL v0, Cursor, Manus, Same.dev, Lovable, Devin, ...
2025-06-30 14:38:19,536 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:19,536 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:19,537 - translator - INFO - 正在翻译项目描述: All the open source AI Agents hosted on the oTToma...
2025-06-30 14:38:27,626 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:27,626 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:27,627 - translator - INFO - 正在翻译项目描述: An LLM-powered knowledge curation system that rese...
2025-06-30 14:38:35,716 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:35,716 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:35,717 - translator - INFO - 正在翻译项目描述: A visual no-code theme editor for shadcn/ui compon...
2025-06-30 14:38:43,795 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:43,795 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:43,795 - translator - INFO - 正在翻译项目描述: 🔥 Turn entire websites into LLM-ready markdown or ...
2025-06-30 14:38:51,896 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:51,897 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:51,897 - translator - INFO - 正在翻译项目描述: Perplexica is an AI-powered search engine. It is a...
2025-06-30 14:38:59,970 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:38:59,971 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:38:59,971 - translator - INFO - 正在翻译项目描述: BookLore is a web app for hosting, managing, and e...
2025-06-30 14:39:08,046 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:08,047 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:08,047 - translator - INFO - 正在翻译项目描述: A list of useful payloads and bypass for Web Appli...
2025-06-30 14:39:16,189 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:16,190 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:16,193 - translator - INFO - 正在翻译项目描述: A self hosted virtual browser that runs in docker ...
2025-06-30 14:39:24,296 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:24,297 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:24,297 - translator - INFO - 正在翻译项目描述: Supercharge Your LLM with the Fastest KV Cache Lay...
2025-06-30 14:39:32,386 - httpx - INFO - HTTP Request: POST https://gpt-best.apifox.cn/chat/completions "HTTP/1.1 400 Bad Request"
2025-06-30 14:39:32,387 - translator - ERROR - 翻译失败: Bad Request
2025-06-30 14:39:32,387 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:39:32,388 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_143932.md
2025-06-30 14:42:54,199 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:42:54,199 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:42:54,199 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:43:10,859 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:43:10,949 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:43:10,949 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:43:10,950 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:43:10,951 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_144310.md
2025-06-30 14:44:17,634 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 14:44:17,634 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 14:44:17,634 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 14:44:19,119 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 14:44:19,204 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 14:44:19,204 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 14:44:19,206 - report_generator - INFO - Markdown报告生成完成
2025-06-30 14:44:19,207 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_144419.md
2025-06-30 15:21:05,383 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 15:21:09,720 - __main__ - INFO - ==================================================
2025-06-30 15:21:09,721 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 15:21:09,721 - __main__ - INFO - ==================================================
2025-06-30 15:21:09,721 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 15:21:09,721 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 15:21:09,721 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 15:21:29,164 - github_spider - WARNING - 获取页面失败 (尝试 1/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 15:21:34,165 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 15:21:53,610 - github_spider - WARNING - 获取页面失败 (尝试 2/3): ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-06-30 15:21:58,611 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 3/3)
2025-06-30 15:22:19,670 - github_spider - WARNING - 获取页面失败 (尝试 3/3): HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /trending?since=daily (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001AFD9A59450>, 'Connection to github.com timed out. (connect timeout=30)'))
2025-06-30 15:22:19,671 - github_spider - ERROR - 获取页面最终失败: https://github.com/trending?since=daily
2025-06-30 15:22:19,671 - github_spider - ERROR - 无法获取页面内容
2025-06-30 15:22:19,671 - __main__ - ERROR - 未能获取到任何仓库数据，任务终止
2025-06-30 15:23:04,936 - __main__ - INFO - 用户中断，程序退出
2025-06-30 15:35:06,667 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 15:35:11,521 - __main__ - INFO - ==================================================
2025-06-30 15:35:11,522 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 15:35:11,522 - __main__ - INFO - ==================================================
2025-06-30 15:35:11,522 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 15:35:11,522 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 15:35:11,522 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 15:35:13,176 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 15:35:13,264 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 15:35:13,265 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 15:35:13,265 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 15:35:13,267 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 15:35:13,268 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 15:35:13,268 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 15:35:13,268 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 15:35:13,269 - report_generator - INFO - Markdown报告生成完成
2025-06-30 15:35:13,270 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 15:35:13,270 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 15:35:13,271 - __main__ - INFO - 步骤 4: 清理旧数据
2025-06-30 15:35:13,272 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 15:35:13,272 - __main__ - INFO - ==================================================
2025-06-30 15:35:13,273 - __main__ - INFO - 每日任务执行完成
2025-06-30 15:35:13,273 - __main__ - INFO - ==================================================
2025-06-30 15:38:06,873 - __main__ - INFO - 用户中断，程序退出
