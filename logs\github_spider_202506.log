2025-06-30 12:03:02,847 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 12:03:02,847 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 12:03:02,847 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 12:03:04,225 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 12:03:04,322 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 12:03:04,323 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 12:03:04,323 - report_generator - INFO - Markdown报告生成完成
2025-06-30 12:03:04,324 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_120304.md
2025-06-30 12:03:16,350 - __main__ - INFO - 获取最近 1 天的统计信息
2025-06-30 12:03:16,350 - data_manager - WARNING - 数据文件不存在: D:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:40,291 - __main__ - INFO - 启动定时任务调度器，每天 12:00 执行
2025-06-30 13:42:51,037 - __main__ - INFO - ==================================================
2025-06-30 13:42:51,037 - __main__ - INFO - 开始执行每日GitHub热门仓库爬取任务
2025-06-30 13:42:51,037 - __main__ - INFO - ==================================================
2025-06-30 13:42:51,037 - __main__ - INFO - 步骤 1: 爬取GitHub热门仓库数据
2025-06-30 13:42:51,037 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:42:51,038 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:42:52,600 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:42:52,688 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:42:52,688 - __main__ - INFO - 成功爬取 16 个仓库
2025-06-30 13:42:52,689 - __main__ - INFO - 步骤 2: 保存原始数据
2025-06-30 13:42:52,690 - data_manager - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:52,690 - __main__ - INFO - 数据已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\data\trending_2025-06-30.json
2025-06-30 13:42:52,690 - __main__ - INFO - 步骤 3: 生成Markdown报告
2025-06-30 13:42:52,690 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:42:52,691 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:42:52,691 - report_generator - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 13:42:52,692 - __main__ - INFO - 报告已保存到: d:\LSK\02 文件合集\99 personal\01-augment\GithubSpider\reports\GitHub热门项目_2025-06-30.md
2025-06-30 13:42:52,692 - __main__ - INFO - 步骤 4: 清理旧数据
2025-06-30 13:42:52,694 - data_manager - INFO - 清理完成，删除了 0 个旧数据文件
2025-06-30 13:42:52,694 - __main__ - INFO - ==================================================
2025-06-30 13:42:52,694 - __main__ - INFO - 每日任务执行完成
2025-06-30 13:42:52,695 - __main__ - INFO - ==================================================
2025-06-30 13:49:31,646 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:49:31,647 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:49:31,647 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:49:33,184 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:49:33,266 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:49:33,267 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:49:33,271 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:49:33,272 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_134933.md
2025-06-30 13:50:21,440 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:50:21,440 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:50:21,441 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:50:51,641 - github_spider - WARNING - 获取页面失败 (尝试 1/3): HTTPSConnectionPool(host='github.com', port=443): Read timed out. (read timeout=30)
2025-06-30 13:50:56,642 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 2/3)
2025-06-30 13:50:58,427 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:50:58,508 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:50:58,509 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:50:58,510 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:50:58,511 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135058.md
2025-06-30 13:51:06,609 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:51:06,609 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:51:06,609 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:51:09,357 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:51:09,450 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:51:09,451 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:51:09,452 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:51:09,452 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135109.md
2025-06-30 13:51:20,076 - __main__ - INFO - 手动执行爬取任务 (周期: daily)
2025-06-30 13:51:20,077 - github_spider - INFO - 开始爬取GitHub热门仓库 (daily)
2025-06-30 13:51:20,077 - github_spider - INFO - 正在获取页面: https://github.com/trending?since=daily (尝试 1/3)
2025-06-30 13:51:21,637 - github_spider - INFO - 成功获取页面，状态码: 200
2025-06-30 13:51:21,717 - github_spider - INFO - 成功爬取 16 个仓库信息
2025-06-30 13:51:21,717 - report_generator - INFO - 加载了 16 个仓库数据
2025-06-30 13:51:21,718 - report_generator - INFO - Markdown报告生成完成
2025-06-30 13:51:21,719 - __main__ - INFO - 报告已生成: GitHub热门项目_daily_20250630_135121.md
