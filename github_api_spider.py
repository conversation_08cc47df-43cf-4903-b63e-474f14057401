"""
GitHub API爬虫 - 替代方案
使用GitHub API获取热门仓库，通常比网页爬虫更稳定
"""

import requests
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from config import *

logger = logging.getLogger(__name__)

class GitHubAPISpider:
    """GitHub API爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GitHub-Trending-Spider/1.0'
        })
        
        # 配置代理（如果有）
        if PROXIES:
            self.session.proxies.update(PROXIES)
            logger.info(f"已配置代理: {PROXIES}")
    
    def get_trending_repositories(self, period: str = "daily") -> List[Dict]:
        """
        通过GitHub API获取热门仓库
        
        Args:
            period: 时间周期 (daily, weekly, monthly)
            
        Returns:
            仓库信息列表
        """
        logger.info(f"开始通过API获取GitHub热门仓库 ({period})")
        
        # 计算日期范围
        date_ranges = {
            'daily': 1,
            'weekly': 7,
            'monthly': 30
        }
        
        days = date_ranges.get(period, 1)
        since_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        # API查询参数
        params = {
            'q': f'created:>{since_date}',
            'sort': 'stars',
            'order': 'desc',
            'per_page': 30  # 获取30个仓库
        }
        
        try:
            url = "https://api.github.com/search/repositories"
            logger.info(f"正在请求API: {url}")
            
            response = self.session.get(url, params=params, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            
            data = response.json()
            repositories = []
            
            for item in data.get('items', []):
                repo_info = {
                    'name': item['name'],
                    'full_name': item['full_name'],
                    'url': item['html_url'],
                    'description': item['description'] or '',
                    'language': item['language'] or '',
                    'stars': item['stargazers_count'],
                    'forks': item['forks_count'],
                    'today_stars': 0,  # API无法直接获取今日新增star
                    'owner': item['owner']['login'],
                    'created_at': item['created_at'],
                    'updated_at': item['updated_at']
                }
                repositories.append(repo_info)
            
            logger.info(f"成功获取 {len(repositories)} 个仓库信息")
            return repositories
            
        except requests.exceptions.Timeout:
            logger.error(f"API请求超时 (>{REQUEST_TIMEOUT}秒)")
            return []
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return []
        except Exception as e:
            logger.error(f"处理API响应时出错: {e}")
            return []
    
    def get_repository_details(self, full_name: str) -> Optional[Dict]:
        """
        获取仓库详细信息
        
        Args:
            full_name: 仓库全名 (owner/repo)
            
        Returns:
            仓库详细信息
        """
        try:
            url = f"https://api.github.com/repos/{full_name}"
            response = self.session.get(url, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.warning(f"获取仓库详情失败 {full_name}: {e}")
            return None

def test_api_spider():
    """测试API爬虫"""
    print("🚀 测试GitHub API爬虫...")
    
    spider = GitHubAPISpider()
    repos = spider.get_trending_repositories("daily")
    
    if repos:
        print(f"✅ 成功获取 {len(repos)} 个仓库")
        print("\n📋 前5个仓库:")
        for i, repo in enumerate(repos[:5], 1):
            print(f"{i}. {repo['full_name']}")
            print(f"   ⭐ {repo['stars']} stars | 🍴 {repo['forks']} forks")
            print(f"   📝 {repo['description'][:100]}...")
            print()
    else:
        print("❌ 未能获取仓库数据")

if __name__ == "__main__":
    test_api_spider()
