"""
Markdown报告生成器
"""

import json
import logging
from datetime import datetime
from typing import List, Dict
from collections import Counter
from config import *

logger = logging.getLogger(__name__)


class MarkdownReportGenerator:
    """Markdown报告生成器类"""
    
    def __init__(self):
        self.repositories = []
        self.report_date = datetime.now().strftime('%Y-%m-%d')
    
    def load_data(self, repositories: List[Dict]):
        """
        加载仓库数据
        
        Args:
            repositories: 仓库信息列表
        """
        self.repositories = repositories
        logger.info(f"加载了 {len(repositories)} 个仓库数据")
    
    def generate_statistics(self) -> Dict:
        """
        生成统计信息
        
        Returns:
            统计信息字典
        """
        if not self.repositories:
            return {}
        
        # 语言统计
        languages = [repo['language'] for repo in self.repositories if repo['language'] != '未知']
        language_stats = Counter(languages)
        
        # 星数统计
        total_stars = sum(repo['stars'] for repo in self.repositories)
        total_today_stars = sum(repo['today_stars'] for repo in self.repositories)
        avg_stars = total_stars // len(self.repositories) if self.repositories else 0
        
        # 最受欢迎的仓库
        most_starred = max(self.repositories, key=lambda x: x['stars']) if self.repositories else None
        most_today_starred = max(self.repositories, key=lambda x: x['today_stars']) if self.repositories else None
        
        stats = {
            'total_repositories': len(self.repositories),
            'total_stars': total_stars,
            'total_today_stars': total_today_stars,
            'average_stars': avg_stars,
            'language_stats': dict(language_stats.most_common(10)),
            'most_starred': most_starred,
            'most_today_starred': most_today_starred
        }
        
        return stats
    
    def format_number(self, num: int) -> str:
        """
        格式化数字显示
        
        Args:
            num: 数字
            
        Returns:
            格式化后的字符串
        """
        if num >= 1000000:
            return f"{num/1000000:.1f}M"
        elif num >= 1000:
            return f"{num/1000:.1f}K"
        else:
            return str(num)
    
    def generate_header(self) -> str:
        """
        生成报告头部
        
        Returns:
            头部Markdown内容
        """
        header = f"""# {REPORT_TITLE}

> {REPORT_SUBTITLE}

**📅 日期**: {self.report_date}  
**🕐 更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**📊 项目数量**: {len(self.repositories)}

---

"""
        return header
    
    def generate_statistics_section(self, stats: Dict) -> str:
        """
        生成统计信息部分
        
        Args:
            stats: 统计信息
            
        Returns:
            统计信息Markdown内容
        """
        if not INCLUDE_STATISTICS or not stats:
            return ""
        
        section = "## 📊 今日统计\n\n"
        
        # 基础统计
        section += f"- **总项目数**: {stats['total_repositories']}\n"
        section += f"- **总星数**: {self.format_number(stats['total_stars'])}\n"
        section += f"- **今日新增星数**: {self.format_number(stats['total_today_stars'])}\n"
        section += f"- **平均星数**: {self.format_number(stats['average_stars'])}\n\n"
        
        # 最受欢迎项目
        if stats.get('most_starred'):
            most_starred = stats['most_starred']
            section += f"**🌟 最受欢迎项目**: [{most_starred['name']}]({most_starred['url']}) ({self.format_number(most_starred['stars'])} stars)\n\n"
        
        if stats.get('most_today_starred'):
            most_today = stats['most_today_starred']
            section += f"**🔥 今日最热项目**: [{most_today['name']}]({most_today['url']}) (+{self.format_number(most_today['today_stars'])} stars today)\n\n"
        
        # 语言统计
        if INCLUDE_LANGUAGE_STATS and stats.get('language_stats'):
            section += "### 🔤 编程语言分布\n\n"
            for lang, count in stats['language_stats'].items():
                percentage = (count / stats['total_repositories']) * 100
                section += f"- **{lang}**: {count} 项目 ({percentage:.1f}%)\n"
            section += "\n"
        
        section += "---\n\n"
        return section
    
    def generate_repository_list(self) -> str:
        """
        生成仓库列表
        
        Returns:
            仓库列表Markdown内容
        """
        if not self.repositories:
            return "暂无数据\n"
        
        section = "## 🚀 热门项目\n\n"
        
        for i, repo in enumerate(self.repositories, 1):
            section += f"### {i}. [{repo['name']}]({repo['url']})\n\n"
            
            # 项目描述
            section += f"**📝 描述**: {repo['description']}\n\n"
            
            # 项目信息
            info_items = []
            
            if INCLUDE_LANGUAGES and repo['language'] != '未知':
                info_items.append(f"**💻 语言**: {repo['language']}")
            
            if INCLUDE_STARS:
                info_items.append(f"**⭐ Stars**: {self.format_number(repo['stars'])}")
            
            if INCLUDE_FORKS:
                info_items.append(f"**🍴 Forks**: {self.format_number(repo['forks'])}")
            
            if repo['today_stars'] > 0:
                info_items.append(f"**📈 今日**: +{self.format_number(repo['today_stars'])} stars")
            
            if info_items:
                section += " | ".join(info_items) + "\n\n"
            
            # 作者信息
            if INCLUDE_AUTHORS and repo['authors']:
                authors_links = [f"[@{author}](https://github.com/{author})" for author in repo['authors'][:3]]
                section += f"**👥 主要贡献者**: {', '.join(authors_links)}\n\n"
            
            section += "---\n\n"
        
        return section
    
    def generate_footer(self) -> str:
        """
        生成报告尾部
        
        Returns:
            尾部Markdown内容
        """
        footer = f"""## 📌 说明

- 数据来源: [GitHub Trending](https://github.com/trending)
- 更新频率: 每日 12:00 自动更新
- 项目排序: 按GitHub热门程度排序

## 🔗 相关链接

- [GitHub Trending](https://github.com/trending)
- [GitHub Trending - Weekly](https://github.com/trending?since=weekly)
- [GitHub Trending - Monthly](https://github.com/trending?since=monthly)

---

*本报告由 GitHub Spider 自动生成 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        return footer
    
    def generate_report(self, repositories: List[Dict] = None) -> str:
        """
        生成完整的Markdown报告
        
        Args:
            repositories: 仓库信息列表（可选）
            
        Returns:
            完整的Markdown报告内容
        """
        if repositories:
            self.load_data(repositories)
        
        if not self.repositories:
            logger.warning("没有仓库数据，无法生成报告")
            return "# 暂无数据\n\n今日暂无热门项目数据。\n"
        
        # 生成统计信息
        stats = self.generate_statistics()
        
        # 组装报告
        report_content = ""
        report_content += self.generate_header()
        report_content += self.generate_statistics_section(stats)
        report_content += self.generate_repository_list()
        report_content += self.generate_footer()
        
        logger.info("Markdown报告生成完成")
        return report_content
    
    def save_report(self, content: str, filename: str = None) -> str:
        """
        保存报告到文件
        
        Args:
            content: 报告内容
            filename: 文件名（可选）
            
        Returns:
            保存的文件路径
        """
        if not filename:
            filename = REPORT_FILE_TEMPLATE.format(date=self.report_date)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"报告已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            raise


if __name__ == "__main__":
    # 测试代码
    generator = MarkdownReportGenerator()
    test_repos = [
        {
            'name': 'test/repo',
            'url': 'https://github.com/test/repo',
            'description': '这是一个测试仓库',
            'language': 'Python',
            'stars': 1000,
            'forks': 200,
            'today_stars': 50,
            'authors': ['testuser'],
            'crawl_time': datetime.now().isoformat()
        }
    ]
    
    report = generator.generate_report(test_repos)
    print(report)
