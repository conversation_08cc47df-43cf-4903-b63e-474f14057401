"""
Markdown报告生成器
"""

import json
import logging
from datetime import datetime
from typing import List, Dict
from collections import Counter
from config import *

logger = logging.getLogger(__name__)


class MarkdownReportGenerator:
    """Markdown报告生成器类"""
    
    def __init__(self):
        self.repositories = []
        self.report_date = datetime.now().strftime('%Y-%m-%d')
    
    def load_data(self, repositories: List[Dict]):
        """
        加载仓库数据
        
        Args:
            repositories: 仓库信息列表
        """
        self.repositories = repositories
        logger.info(f"加载了 {len(repositories)} 个仓库数据")
    
    def generate_statistics(self) -> Dict:
        """
        生成统计信息
        
        Returns:
            统计信息字典
        """
        if not self.repositories:
            return {}
        
        # 语言统计
        languages = [repo['language'] for repo in self.repositories if repo['language'] != '未知']
        language_stats = Counter(languages)
        
        # 星数统计
        total_stars = sum(repo['stars'] for repo in self.repositories)
        total_today_stars = sum(repo['today_stars'] for repo in self.repositories)
        avg_stars = total_stars // len(self.repositories) if self.repositories else 0
        
        # 最受欢迎的仓库
        most_starred = max(self.repositories, key=lambda x: x['stars']) if self.repositories else None
        most_today_starred = max(self.repositories, key=lambda x: x['today_stars']) if self.repositories else None
        
        stats = {
            'total_repositories': len(self.repositories),
            'total_stars': total_stars,
            'total_today_stars': total_today_stars,
            'average_stars': avg_stars,
            'language_stats': dict(language_stats.most_common(10)),
            'most_starred': most_starred,
            'most_today_starred': most_today_starred
        }
        
        return stats
    
    def format_number(self, num: int) -> str:
        """
        格式化数字显示
        
        Args:
            num: 数字
            
        Returns:
            格式化后的字符串
        """
        if num >= 1000000:
            return f"{num/1000000:.1f}M"
        elif num >= 1000:
            return f"{num/1000:.1f}K"
        else:
            return str(num)
    
    def generate_header(self) -> str:
        """
        生成报告头部

        Returns:
            头部Markdown内容
        """
        # 获取当前时间的一些有趣信息
        now = datetime.now()
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        weekday = weekday_names[now.weekday()]

        # 计算一些统计数据
        total_stars = sum(repo['stars'] for repo in self.repositories)
        total_today_stars = sum(repo['today_stars'] for repo in self.repositories)

        header = f"""# {REPORT_TITLE} 🚀

> {REPORT_SUBTITLE} | 发现优质开源项目，跟上技术发展趋势

## 📋 报告概览

| 📊 统计项 | 📈 数值 | 📝 说明 |
|---------|--------|--------|
| 📅 **报告日期** | `{self.report_date}` ({weekday}) | GitHub Trending 每日快照 |
| 🕐 **数据时间** | `{now.strftime('%H:%M:%S')}` | 实时爬取生成 |
| 🎯 **项目总数** | `{len(self.repositories)}` 个 | 精选热门开源项目 |
| ⭐ **总星数** | `{self.format_number(total_stars)}` | 社区认可度指标 |
| 🔥 **今日热度** | `+{self.format_number(total_today_stars)}` | 24小时新增关注 |

---

"""
        return header
    
    def generate_statistics_section(self, stats: Dict) -> str:
        """
        生成统计信息部分

        Args:
            stats: 统计信息

        Returns:
            统计信息Markdown内容
        """
        if not INCLUDE_STATISTICS or not stats:
            return ""

        section = "## 📊 数据洞察\n\n"

        # 核心指标卡片
        section += "### 🎯 核心指标\n\n"
        section += "```\n"
        section += f"📦 项目总览    {stats['total_repositories']:>8} 个精选项目\n"
        section += f"⭐ 社区认可    {self.format_number(stats['total_stars']):>8} 总星标数\n"
        section += f"🔥 今日热度    {self.format_number(stats['total_today_stars']):>8} 新增关注\n"
        section += f"📈 平均质量    {self.format_number(stats['average_stars']):>8} 平均星标\n"
        section += "```\n\n"

        # 热门项目榜
        section += "### 🏆 今日榜单\n\n"

        if stats.get('most_starred'):
            most_starred = stats['most_starred']
            clean_name = most_starred['name'].strip().replace('\n', '').replace('\r', '')
            section += f"🥇 **人气王**: [{clean_name}]({most_starred['url']})  \n"
            section += f"   └─ {self.format_number(most_starred['stars'])} stars | {most_starred.get('language', '未知')} 项目\n\n"

        if stats.get('most_today_starred'):
            most_today = stats['most_today_starred']
            clean_name = most_today['name'].strip().replace('\n', '').replace('\r', '')
            section += f"🚀 **增长王**: [{clean_name}]({most_today['url']})  \n"
            section += f"   └─ +{self.format_number(most_today['today_stars'])} stars today | {most_today.get('language', '未知')} 项目\n\n"

        # 技术栈分析
        if INCLUDE_LANGUAGE_STATS and stats.get('language_stats'):
            section += "### 💻 技术栈分析\n\n"

            # 创建语言热度图
            total_repos = stats['total_repositories']
            for i, (lang, count) in enumerate(stats['language_stats'].items()):
                percentage = (count / total_repos) * 100

                # 生成进度条
                bar_length = int(percentage / 5)  # 每5%一个字符
                bar = "█" * bar_length + "░" * (20 - bar_length)

                # 语言图标
                lang_icons = {
                    'Python': '🐍', 'JavaScript': '⚡', 'TypeScript': '🔷',
                    'Java': '☕', 'Go': '🐹', 'Rust': '🦀', 'C++': '⚙️',
                    'Swift': '🍎', 'Kotlin': '🎯', 'Vue': '💚', 'React': '⚛️',
                    'HTML': '🌐', 'CSS': '🎨', 'Jupyter Notebook': '📊'
                }
                icon = lang_icons.get(lang, '📝')

                section += f"{icon} **{lang}**  \n"
                section += f"   `{bar}` {count} 项目 ({percentage:.1f}%)  \n\n"

                if i >= 7:  # 只显示前8种语言
                    break

        # 趋势分析
        section += "### 📈 趋势观察\n\n"

        # 分析今日热度分布
        high_growth = [repo for repo in self.repositories if repo['today_stars'] >= 100]
        medium_growth = [repo for repo in self.repositories if 50 <= repo['today_stars'] < 100]

        section += f"- 🔥 **爆发增长** (≥100 stars): {len(high_growth)} 个项目\n"
        section += f"- 📊 **稳定增长** (50-99 stars): {len(medium_growth)} 个项目\n"
        section += f"- 📈 **持续关注** (<50 stars): {len(self.repositories) - len(high_growth) - len(medium_growth)} 个项目\n\n"

        # AI/ML项目统计
        ai_keywords = ['ai', 'ml', 'llm', 'gpt', 'neural', 'deep', 'learning', 'intelligence']
        ai_projects = []
        for repo in self.repositories:
            desc_lower = repo['description'].lower()
            name_lower = repo['name'].lower()
            if any(keyword in desc_lower or keyword in name_lower for keyword in ai_keywords):
                ai_projects.append(repo)

        if ai_projects:
            section += f"- 🤖 **AI/ML 相关**: {len(ai_projects)} 个项目 ({len(ai_projects)/len(self.repositories)*100:.1f}%)\n"

        section += "\n---\n\n"
        return section
    
    def translate_description(self, description: str) -> str:
        """
        翻译项目描述为中文（简单的关键词替换）

        Args:
            description: 英文描述

        Returns:
            中文描述
        """
        if not description or description == "暂无描述":
            return "暂无描述"

        # 简单的关键词翻译映射
        translations = {
            "Building a modern alternative to": "构建现代化的替代方案，替代",
            "powered by the community": "由社区驱动",
            "A self hosted": "一个自托管的",
            "Turn entire websites into": "将整个网站转换为",
            "LLM-ready markdown": "适用于大语言模型的Markdown",
            "structured data": "结构化数据",
            "Scrape, crawl and extract": "抓取、爬取和提取",
            "with a single API": "通过单一API",
            "An LLM-powered": "基于大语言模型的",
            "knowledge curation system": "知识管理系统",
            "researches a topic": "研究主题",
            "generates a full-length report": "生成完整报告",
            "with citations": "包含引用",
            "A visual no-code": "可视化无代码",
            "theme editor": "主题编辑器",
            "components": "组件",
            "AI-powered search engine": "AI驱动的搜索引擎",
            "Open source alternative": "开源替代方案",
            "web app for hosting": "用于托管的网页应用",
            "managing, and exploring": "管理和探索",
            "reading progress": "阅读进度",
            "metadata, and stats": "元数据和统计",
            "useful payloads and bypass": "有用的载荷和绕过技术",
            "Web Application Security": "Web应用安全",
            "Pentest/CTF": "渗透测试/CTF",
            "virtual browser": "虚拟浏览器",
            "runs in docker": "运行在Docker中",
            "uses WebRTC": "使用WebRTC技术",
            "Supercharge Your LLM": "为你的大语言模型增压",
            "Fastest KV Cache Layer": "最快的键值缓存层",
            "vector & raster editor": "矢量和栅格编辑器",
            "traditional layers & tools": "传统图层和工具",
            "node-based, non-destructive": "基于节点的非破坏性",
            "procedural workflow": "程序化工作流",
            "Get Started Building": "开始构建",
            "Generative AI": "生成式AI",
            "Lessons": "课程"
        }

        translated = description
        for en, zh in translations.items():
            translated = translated.replace(en, zh)

        return translated

    def get_tech_insights(self, repo: Dict) -> List[str]:
        """
        根据项目信息生成技术洞察

        Args:
            repo: 仓库信息

        Returns:
            技术洞察列表
        """
        insights = []

        # 根据语言添加技术标签
        language_tags = {
            'Python': ['🐍 Python生态', '机器学习友好', '快速开发'],
            'TypeScript': ['🔷 TypeScript', '类型安全', '现代前端'],
            'JavaScript': ['⚡ JavaScript', '全栈开发', '生态丰富'],
            'Rust': ['🦀 Rust', '内存安全', '高性能'],
            'Go': ['🐹 Go', '并发优秀', '云原生'],
            'Java': ['☕ Java', '企业级', '跨平台'],
            'C++': ['⚙️ C++', '系统级', '高性能'],
            'Swift': ['🍎 Swift', 'iOS开发', '现代语法'],
            'Kotlin': ['🎯 Kotlin', 'Android开发', 'Java互操作'],
            'Vue': ['💚 Vue.js', '渐进式框架', '易学易用'],
            'React': ['⚛️ React', '组件化', '生态强大'],
            'HTML': ['🌐 Web技术', '前端基础', '标准化'],
            'CSS': ['🎨 样式设计', '视觉效果', '响应式'],
            'Jupyter Notebook': ['📊 数据科学', '交互式', '可视化']
        }

        if repo['language'] in language_tags:
            insights.extend(language_tags[repo['language']][:2])

        # 根据星数添加热度标签
        stars = repo['stars']
        if stars >= 50000:
            insights.append('🔥 超高人气')
        elif stars >= 10000:
            insights.append('⭐ 高度关注')
        elif stars >= 1000:
            insights.append('📈 快速增长')

        # 根据今日星数添加趋势标签
        today_stars = repo['today_stars']
        if today_stars >= 500:
            insights.append('🚀 爆发式增长')
        elif today_stars >= 100:
            insights.append('📊 强劲增长')
        elif today_stars >= 50:
            insights.append('📈 稳定增长')

        return insights[:3]  # 最多返回3个标签

    def generate_repository_list(self) -> str:
        """
        生成仓库列表

        Returns:
            仓库列表Markdown内容
        """
        if not self.repositories:
            return "暂无数据\n"

        section = "## 🚀 热门项目\n\n"

        for i, repo in enumerate(self.repositories, 1):
            # 修复项目名称和链接格式
            clean_name = repo['name'].strip().replace('\n', '').replace('\r', '')
            section += f"### {i}. [{clean_name}]({repo['url']})\n\n"

            # 翻译项目描述
            translated_desc = self.translate_description(repo['description'])
            section += f"**📝 项目简介**: {translated_desc}\n\n"

            # 技术洞察标签
            tech_insights = self.get_tech_insights(repo)
            if tech_insights:
                section += f"**🏷️ 技术标签**: {' · '.join(tech_insights)}\n\n"

            # 项目信息
            info_items = []

            if INCLUDE_LANGUAGES and repo['language'] != '未知':
                info_items.append(f"**💻 主语言**: {repo['language']}")

            if INCLUDE_STARS:
                info_items.append(f"**⭐ Stars**: {self.format_number(repo['stars'])}")

            if INCLUDE_FORKS:
                info_items.append(f"**🍴 Forks**: {self.format_number(repo['forks'])}")

            if repo['today_stars'] > 0:
                info_items.append(f"**📈 今日新增**: +{self.format_number(repo['today_stars'])} stars")

            if info_items:
                section += " | ".join(info_items) + "\n\n"

            # 作者信息
            if INCLUDE_AUTHORS and repo['authors']:
                authors_links = [f"[@{author}](https://github.com/{author})" for author in repo['authors'][:3]]
                section += f"**👥 核心贡献者**: {', '.join(authors_links)}\n\n"

            # 添加项目亮点
            highlights = []
            if repo['today_stars'] >= 200:
                highlights.append("🔥 今日热门")
            if repo['stars'] >= 20000:
                highlights.append("⭐ 明星项目")
            if repo['forks'] >= 1000:
                highlights.append("🍴 活跃社区")

            if highlights:
                section += f"**✨ 项目亮点**: {' · '.join(highlights)}\n\n"

            section += "---\n\n"

        return section
    
    def generate_footer(self) -> str:
        """
        生成报告尾部

        Returns:
            尾部Markdown内容
        """
        now = datetime.now()

        footer = f"""## 🛠️ 开发者工具箱

### 📚 学习资源
- 🎓 [GitHub 官方文档](https://docs.github.com/) - 掌握 Git 和 GitHub 最佳实践
- 📖 [开源指南](https://opensource.guide/) - 学习如何参与开源项目
- 🔍 [GitHub Search](https://github.com/search) - 高级搜索技巧发现更多项目

### 🔧 实用工具
- 📊 [GitHub Stats](https://github-readme-stats.vercel.app/) - 生成个人 GitHub 统计卡片
- 🏆 [GitHub Achievements](https://github.com/Schweinepriester/github-profile-achievements) - 解锁 GitHub 成就徽章
- 📈 [Gitpod](https://gitpod.io/) - 云端开发环境，一键启动项目

### 🌟 参与开源
- 🐛 **报告问题**: 发现 bug 及时提交 Issue
- 💡 **提出建议**: 分享你的想法和改进建议
- 🔧 **贡献代码**: 提交 Pull Request 参与项目开发
- 📝 **完善文档**: 帮助改进项目文档和示例

## 📊 数据说明

| 项目 | 说明 |
|------|------|
| **数据来源** | [GitHub Trending](https://github.com/trending) 官方榜单 |
| **更新频率** | 每日 12:00 自动爬取生成 |
| **排序规则** | 按 GitHub 算法综合热度排序 |
| **数据时效** | 实时反映过去 24 小时趋势 |
| **项目筛选** | 自动过滤优质开源项目 |

## 🔗 快速导航

### 📅 时间维度
- 📈 [今日热门](https://github.com/trending?since=daily) - 24小时内最热项目
- 📊 [本周热门](https://github.com/trending?since=weekly) - 7天内持续热门
- 🏆 [本月热门](https://github.com/trending?since=monthly) - 30天内最受关注

### 💻 语言分类
- 🐍 [Python](https://github.com/trending/python) - 数据科学与AI首选
- ⚡ [JavaScript](https://github.com/trending/javascript) - 前端开发必备
- 🔷 [TypeScript](https://github.com/trending/typescript) - 大型项目首选
- 🦀 [Rust](https://github.com/trending/rust) - 系统编程新星
- 🐹 [Go](https://github.com/trending/go) - 云原生开发利器

---

<div align="center">

**🤖 本报告由 GitHub Spider 自动生成**

📅 生成时间: `{now.strftime('%Y-%m-%d %H:%M:%S')}`
⚡ 数据更新: 每日 12:00 定时执行
🔄 下次更新: 明日 12:00

*持续关注开源动态，发现优质项目 🚀*

</div>
"""
        return footer
    
    def generate_report(self, repositories: List[Dict] = None) -> str:
        """
        生成完整的Markdown报告
        
        Args:
            repositories: 仓库信息列表（可选）
            
        Returns:
            完整的Markdown报告内容
        """
        if repositories:
            self.load_data(repositories)
        
        if not self.repositories:
            logger.warning("没有仓库数据，无法生成报告")
            return "# 暂无数据\n\n今日暂无热门项目数据。\n"
        
        # 生成统计信息
        stats = self.generate_statistics()
        
        # 组装报告
        report_content = ""
        report_content += self.generate_header()
        report_content += self.generate_statistics_section(stats)
        report_content += self.generate_repository_list()
        report_content += self.generate_footer()
        
        logger.info("Markdown报告生成完成")
        return report_content
    
    def save_report(self, content: str, filename: str = None) -> str:
        """
        保存报告到文件
        
        Args:
            content: 报告内容
            filename: 文件名（可选）
            
        Returns:
            保存的文件路径
        """
        if not filename:
            filename = REPORT_FILE_TEMPLATE.format(date=self.report_date)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"报告已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            raise


if __name__ == "__main__":
    # 测试代码
    generator = MarkdownReportGenerator()
    test_repos = [
        {
            'name': 'test/repo',
            'url': 'https://github.com/test/repo',
            'description': '这是一个测试仓库',
            'language': 'Python',
            'stars': 1000,
            'forks': 200,
            'today_stars': 50,
            'authors': ['testuser'],
            'crawl_time': datetime.now().isoformat()
        }
    ]
    
    report = generator.generate_report(test_repos)
    print(report)
