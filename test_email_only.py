"""
邮件发送专项测试 - 测试修复后的邮件编码问题
"""

import os
from email_sender import EmailSender

def test_email_sending():
    """测试邮件发送功能"""
    print("📧 测试邮件发送功能")
    print("=" * 40)
    
    try:
        # 创建邮件发送器
        email_sender = EmailSender()
        print("✅ 邮件发送器创建成功")
        
        # 测试连接
        print("🔗 测试SMTP连接...")
        if email_sender.test_connection():
            print("✅ SMTP连接成功")
        else:
            print("❌ SMTP连接失败")
            return False
        
        # 查找现有报告文件
        reports_dir = "reports"
        if os.path.exists(reports_dir):
            report_files = [f for f in os.listdir(reports_dir) if f.endswith('.md')]
            if report_files:
                report_file = os.path.join(reports_dir, report_files[-1])  # 使用最新的报告
                print(f"📄 使用报告文件: {report_file}")
                
                # 发送邮件
                print("📤 发送邮件...")
                if email_sender.send_report(report_file):
                    print("✅ 邮件发送成功！")
                    return True
                else:
                    print("❌ 邮件发送失败")
                    return False
            else:
                print("❌ 未找到报告文件")
                return False
        else:
            print("❌ 报告目录不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_email_sending()
