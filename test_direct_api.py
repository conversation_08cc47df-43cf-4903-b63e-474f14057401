"""
直接测试DeepSeek官方API调用
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# API配置
api_key = os.environ.get("DEEPSEEK_API_KEY")
base_url = "https://api.deepseek.com"
endpoint = f"{base_url}/chat/completions"

print(f"🔍 测试DeepSeek官方API")
print(f"📡 API端点: {endpoint}")
print(f"🔑 API密钥: {api_key[:20] if api_key else 'None'}...")

if not api_key:
    print("❌ 错误: 未找到API密钥，请检查.env文件")
    exit(1)

# 请求头
headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": f"Bearer {api_key}"
}

# 请求数据
data = {
    "model": "deepseek-chat",  # 使用官方推荐模型
    "messages": [
        {
            "role": "user",
            "content": "请将以下英文翻译成中文，要求通俗易懂：Building a modern alternative to Salesforce, powered by the community."
        }
    ],
    "max_tokens": 200,
    "temperature": 0.3
}

print("\n🔄 发送API请求...")
print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

try:
    response = requests.post(endpoint, headers=headers, json=data, timeout=30)
    
    print(f"\n📊 响应状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ API调用成功!")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if 'choices' in result and len(result['choices']) > 0:
            translated_text = result['choices'][0]['message']['content']
            print(f"\n🎉 翻译结果: {translated_text}")
        else:
            print("❌ 响应格式异常，没有找到翻译结果")
    else:
        print(f"❌ API调用失败")
        print(f"错误响应: {response.text}")
        
except requests.exceptions.RequestException as e:
    print(f"❌ 请求异常: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
