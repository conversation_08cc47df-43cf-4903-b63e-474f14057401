@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    GitHub热门仓库爬虫启动脚本
echo ========================================
echo.
echo 请选择运行模式:
echo 1. 定时任务模式 (每天12点自动执行)
echo 2. 手动执行 - 今日热门
echo 3. 手动执行 - 本周热门  
echo 4. 手动执行 - 本月热门
echo 5. 查看统计信息
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" (
    echo 启动定时任务模式...
    python main.py --mode schedule
) else if "%choice%"=="2" (
    echo 执行今日热门爬取...
    python main.py --mode manual --period daily
) else if "%choice%"=="3" (
    echo 执行本周热门爬取...
    python main.py --mode manual --period weekly
) else if "%choice%"=="4" (
    echo 执行本月热门爬取...
    python main.py --mode manual --period monthly
) else if "%choice%"=="5" (
    echo 显示统计信息...
    python main.py --mode stats --days 7
) else if "%choice%"=="6" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，请重新运行脚本
)

echo.
pause
