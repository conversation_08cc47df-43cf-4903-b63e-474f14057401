# 📁 GitHub热门项目爬虫 - 项目结构说明

## 🎯 项目概述
自动爬取GitHub热门项目，使用AI翻译生成中文报告，并通过企业微信邮箱发送日报。

## 📂 文件结构和功能说明

### 🔧 核心程序文件 (8个)

#### 1. `main.py` - 主程序入口
- **功能**: GitHub热门项目爬虫的核心控制器
- **作用**: 爬取GitHub热门项目、生成报告、发送邮件、定时任务调度
- **使用**: `python main.py` 或 `python main.py --mode daily`

#### 2. `daily_task.py` - 每日定时任务脚本  
- **功能**: 专门用于Code Runner和Windows定时任务执行
- **作用**: 执行完整的爬取→翻译→生成报告→发送邮件流程
- **使用**: `python daily_task.py` (推荐用于定时任务)

#### 3. `github_spider.py` - GitHub爬虫模块
- **功能**: 负责爬取GitHub热门项目数据
- **作用**: 网页解析、项目信息提取、数据清洗、错误重试
- **核心类**: `GitHubSpider`

#### 4. `translator.py` - AI翻译模块
- **功能**: 使用DeepSeek R1 API进行项目描述的智能翻译
- **作用**: 英文项目描述翻译为中文、缓存机制、错误重试
- **核心类**: `ProjectTranslator`

#### 5. `report_generator.py` - 报告生成器模块
- **功能**: 负责生成美观的Markdown格式项目报告
- **作用**: Markdown格式化、统计图表、项目分类、样式美化
- **核心类**: `ReportGenerator`

#### 6. `email_sender.py` - 邮件发送模块
- **功能**: 负责发送GitHub热门项目报告邮件到指定邮箱
- **作用**: SMTP邮件发送、HTML格式、Markdown附件
- **核心类**: `EmailSender`

#### 7. `data_manager.py` - 数据管理模块
- **功能**: 负责项目数据的存储、读取和管理
- **作用**: JSON文件操作、数据验证、历史数据管理、备份恢复
- **核心类**: `DataManager`

#### 8. `config.py` - 配置文件模块
- **功能**: 存储项目的所有配置参数和常量
- **作用**: 爬虫配置、定时任务配置、报告配置、系统参数
- **内容**: 各种配置常量和参数

### ⚙️ 配置文件 (4个)

#### 1. `.env` - 环境变量配置
- **功能**: 存储敏感信息和环境配置
- **内容**: DeepSeek API密钥、企业微信邮箱配置
- **安全**: 已添加到.gitignore，不会提交到版本控制

#### 2. `requirements.txt` - Python依赖管理
- **功能**: 项目依赖包列表
- **使用**: `pip install -r requirements.txt`

#### 3. `.gitignore` - Git忽略规则
- **功能**: 指定Git忽略的文件和目录
- **内容**: 忽略.env、__pycache__、日志文件等

#### 4. `.vscode/settings.json` - Code Runner配置
- **功能**: VSCode Code Runner扩展配置
- **作用**: 支持一键运行Python脚本

### 🧪 测试文件 (2个)

#### 1. `test_email_simple.py` - 邮件测试模块
- **功能**: 测试企业微信邮箱的SMTP连接和发送功能
- **作用**: SMTP连接测试、邮件发送测试、配置验证
- **使用**: `python test_email_simple.py`

#### 2. `test_api.py` - API测试模块
- **功能**: 测试DeepSeek翻译API的连接和功能
- **作用**: API连接测试、翻译功能测试、错误诊断
- **使用**: `python test_api.py`

### 📚 文档和工具 (4个)

#### 1. `README.md` - 项目说明文档
- **功能**: 项目介绍、安装指南、使用说明
- **内容**: 完整的项目文档

#### 2. `CODE_RUNNER_SETUP.md` - Code Runner配置指南
- **功能**: VSCode Code Runner扩展的配置说明
- **内容**: 详细的配置步骤和使用方法

#### 3. `LICENSE` - 开源许可证
- **功能**: 项目的开源许可证
- **类型**: MIT License

#### 4. `setup_scheduler.bat` - Windows定时任务设置脚本
- **功能**: 自动设置Windows定时任务
- **作用**: 每天12:00自动执行daily_task.py
- **使用**: 以管理员身份运行

### 📁 数据目录 (3个)

#### 1. `data/` - 爬取数据存储目录
- **内容**: `trending_YYYY-MM-DD.json` 格式的数据文件
- **作用**: 存储每日爬取的GitHub热门项目数据

#### 2. `reports/` - 生成报告目录
- **内容**: `GitHub热门项目_YYYY-MM-DD.md` 格式的报告文件
- **作用**: 存储生成的Markdown格式日报

#### 3. `logs/` - 日志文件目录
- **内容**: `github_spider_YYYYMM.log` 格式的日志文件
- **作用**: 记录程序运行日志和错误信息

## 🚀 使用流程

### 1. 手动执行
```bash
python daily_task.py
```

### 2. Code Runner执行
- 在VSCode中打开 `daily_task.py`
- 按 `Ctrl+Alt+N` 执行

### 3. 定时任务执行
```bash
# 以管理员身份运行
setup_scheduler.bat
```

### 4. 测试功能
```bash
# 测试邮件发送
python test_email_simple.py

# 测试API翻译
python test_api.py
```

## 🔧 配置要求

### 环境变量 (.env文件)
```env
# DeepSeek API配置
DEEPSEEK_API_KEY="your_api_key"

# 企业微信邮箱配置
EMAIL_SMTP_SERVER="smtp.exmail.qq.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"
EMAIL_PASSWORD="your_password"
EMAIL_TO="<EMAIL>"
```

### Python依赖
- requests
- beautifulsoup4
- openai
- python-dotenv
- schedule

---

**📋 总计文件数**: 21个
- 核心程序: 8个
- 配置文件: 4个  
- 测试文件: 2个
- 文档工具: 4个
- 数据目录: 3个
