# GitHub热门仓库爬虫 🚀

一个自动化的GitHub热门仓库爬虫工具，每天定时抓取GitHub Trending页面的热门项目，并生成精美的Markdown格式报告。

## ✨ 功能特性

- 🕐 **智能定时**: 每天上午12点自动执行爬虫任务
- 📊 **精准采集**: 自动抓取GitHub热门仓库完整信息
- 📝 **精美报告**: 生成程序员友好的Markdown格式日报
- 💾 **数据管理**: 持久化保存历史数据，支持查询和导出
- 📈 **深度分析**: 提供技术栈分析、趋势观察和AI项目统计
- 🏷️ **智能标签**: 自动生成技术标签和项目亮点
- 🌐 **中文优化**: 智能翻译项目描述，符合中文阅读习惯
- 🔧 **灵活配置**: 支持多种配置选项和运行模式
- 📋 **完整日志**: 详细的日志系统，便于监控和调试

## 🎯 报告特色

### 📊 数据洞察面板
- **核心指标**: 项目总览、社区认可度、今日热度、平均质量
- **今日榜单**: 人气王、增长王项目展示
- **技术栈分析**: 可视化进度条显示语言分布
- **趋势观察**: 爆发增长、稳定增长项目统计
- **AI/ML统计**: 自动识别人工智能相关项目

### 🏷️ 智能标签系统
- **技术标签**: 根据编程语言自动生成技术特性标签
- **热度标签**: 基于星数和增长速度的动态标签
- **项目亮点**: 今日热门、明星项目、活跃社区等标识

### 🌐 中文本土化
- **描述翻译**: 智能翻译英文项目描述为中文
- **技术术语**: 专业的技术词汇中文化处理
- **阅读体验**: 符合中文开发者阅读习惯的排版

### 🛠️ 开发者工具箱
- **学习资源**: GitHub官方文档、开源指南等精选链接
- **实用工具**: GitHub统计卡片、成就徽章、云端开发环境
- **参与指南**: 详细的开源贡献指导

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 1. 定时任务模式（推荐）

启动定时任务，每天自动执行：

```bash
python main.py --mode schedule
```

### 2. 手动执行模式

立即执行一次爬取任务：

```bash
# 爬取今日热门
python main.py --mode manual --period daily

# 爬取本周热门
python main.py --mode manual --period weekly

# 爬取本月热门
python main.py --mode manual --period monthly
```

### 3. 统计信息模式

查看历史统计数据：

```bash
# 查看最近7天统计
python main.py --mode stats --days 7

# 查看最近30天统计
python main.py --mode stats --days 30
```

## 📁 项目结构

```
GithubSpider/
├── main.py              # 主程序入口
├── daily_task.py        # 每日定时任务脚本
├── github_spider.py     # GitHub爬虫核心模块
├── report_generator.py  # Markdown报告生成器
├── data_manager.py      # 数据管理模块
├── translator.py        # AI翻译模块
├── email_sender.py      # 邮件发送模块
├── config.py           # 配置文件
├── .env                # 环境变量配置
├── requirements.txt    # 依赖包列表
├── setup_scheduler.bat # Windows定时任务设置
├── test_*.py           # 测试脚本
├── README.md          # 项目说明文档
├── CODE_RUNNER_SETUP.md # Code Runner配置指南
├── data/              # 数据存储目录
│   └── trending_YYYY-MM-DD.json
├── reports/           # 报告输出目录
│   └── GitHub热门项目_YYYY-MM-DD.md
└── logs/             # 日志文件目录
    └── github_spider_YYYYMM.log
```

## 📧 邮件配置

项目支持自动发送邮件报告功能，使用企业微信邮箱发送。

### 配置步骤

1. 编辑 `.env` 文件，设置邮件参数：
```env
EMAIL_SMTP_SERVER="smtp.exmail.qq.com"
EMAIL_SMTP_PORT="587"
EMAIL_FROM="<EMAIL>"
EMAIL_PASSWORD="your_email_password"
EMAIL_TO="<EMAIL>"
```

2. 企业微信邮箱配置：
   - 使用腾讯企业邮箱SMTP服务器
   - 直接使用邮箱登录密码
   - 支持TLS加密传输

## ⚙️ 配置说明

主要配置项在 `config.py` 文件中：

```python
# 定时任务配置
SCHEDULE_TIME = "12:00"  # 每天执行时间

# 爬取配置
MAX_REPOSITORIES = 25    # 每次爬取的最大仓库数量
REQUEST_TIMEOUT = 30     # 请求超时时间（秒）
MAX_RETRIES = 3         # 最大重试次数

# 报告配置
REPORT_TITLE = "GitHub 今日热门项目"
INCLUDE_STATISTICS = True    # 是否包含统计信息
INCLUDE_LANGUAGE_STATS = True # 是否包含语言统计
```

## 📊 数据格式

### 原始数据格式 (JSON)

```json
{
  "date": "2024-01-01",
  "crawl_time": "2024-01-01T12:00:00",
  "total_count": 25,
  "repositories": [
    {
      "name": "owner/repo-name",
      "url": "https://github.com/owner/repo-name",
      "description": "项目描述",
      "language": "Python",
      "stars": 1000,
      "forks": 200,
      "today_stars": 50,
      "authors": ["author1", "author2"],
      "crawl_time": "2024-01-01T12:00:00"
    }
  ]
}
```

### 报告格式 (Markdown)

生成的报告包含以下部分：

#### 📋 报告概览
- 表格形式展示核心统计数据
- 包含日期、时间、项目数量等关键信息

#### 📊 数据洞察
- **核心指标**: ASCII艺术风格的数据展示
- **今日榜单**: 人气王和增长王项目
- **技术栈分析**: 带进度条的语言分布图
- **趋势观察**: 增长分类统计和AI项目占比

#### 🚀 热门项目
- **项目简介**: 中文翻译的项目描述
- **技术标签**: 自动生成的技术特性标签
- **详细信息**: 语言、星数、Fork数、今日增长
- **项目亮点**: 基于数据的项目特色标识
- **核心贡献者**: 主要开发者链接

#### 🛠️ 开发者工具箱
- **学习资源**: 精选的GitHub学习链接
- **实用工具**: 开发者常用工具推荐
- **参与指南**: 开源贡献最佳实践

#### 📊 数据说明 & 🔗 快速导航
- 详细的数据来源和更新说明
- 按时间维度和编程语言分类的快速链接

## 🛠️ 高级用法

### 自定义配置

修改 `config.py` 文件来自定义爬虫行为：

```python
# 修改爬取数量
MAX_REPOSITORIES = 50

# 修改执行时间
SCHEDULE_TIME = "09:00"

# 自定义报告标题
REPORT_TITLE = "我的GitHub热门项目日报"
```

### 数据导出

使用数据管理器导出历史数据：

```python
from data_manager import DataManager

manager = DataManager()
# 导出指定时间范围的数据
manager.export_data('2024-01-01', '2024-01-31', 'export.json')
```

### 清理旧数据

```python
from data_manager import DataManager

manager = DataManager()
# 清理30天前的数据
manager.cleanup_old_data(keep_days=30)
```

## 📋 日志说明

日志文件保存在 `logs/` 目录下，按月份分割：

- **INFO**: 正常运行信息
- **WARNING**: 警告信息（如网络重试）
- **ERROR**: 错误信息（如爬取失败）

## 🔧 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 确认GitHub可访问
   - 考虑使用代理

2. **爬取数据为空**
   - 检查GitHub Trending页面结构是否变化
   - 查看日志文件获取详细错误信息

3. **定时任务不执行**
   - 确认系统时间正确
   - 检查程序是否正常运行
   - 查看日志文件

### 调试模式

修改 `config.py` 中的日志级别：

```python
LOG_LEVEL = "DEBUG"  # 显示详细调试信息
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [GitHub](https://github.com) - 提供优秀的开源平台
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) - 强大的HTML解析库
- [Requests](https://docs.python-requests.org/) - 简洁的HTTP库
- [Schedule](https://schedule.readthedocs.io/) - 优雅的任务调度库

---

**⭐ 如果这个项目对你有帮助，请给它一个星标！**
