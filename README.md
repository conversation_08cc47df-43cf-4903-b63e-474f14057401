# GitHub热门仓库爬虫 🚀

一个自动化的GitHub热门仓库爬虫工具，每天定时抓取GitHub Trending页面的热门项目，并生成精美的Markdown格式报告。

## ✨ 功能特性

- 🕐 **定时任务**: 每天上午12点自动执行爬虫任务
- 📊 **数据采集**: 自动抓取GitHub热门仓库信息
- 📝 **报告生成**: 生成美观的Markdown格式日报
- 💾 **数据存储**: 持久化保存历史数据，支持数据查询
- 📈 **统计分析**: 提供项目统计和趋势分析
- 🔧 **灵活配置**: 支持多种配置选项和运行模式
- 📋 **日志记录**: 完整的日志系统，便于监控和调试

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 1. 定时任务模式（推荐）

启动定时任务，每天自动执行：

```bash
python main.py --mode schedule
```

### 2. 手动执行模式

立即执行一次爬取任务：

```bash
# 爬取今日热门
python main.py --mode manual --period daily

# 爬取本周热门
python main.py --mode manual --period weekly

# 爬取本月热门
python main.py --mode manual --period monthly
```

### 3. 统计信息模式

查看历史统计数据：

```bash
# 查看最近7天统计
python main.py --mode stats --days 7

# 查看最近30天统计
python main.py --mode stats --days 30
```

## 📁 项目结构

```
GithubSpider/
├── main.py              # 主程序入口
├── github_spider.py     # GitHub爬虫核心模块
├── report_generator.py  # Markdown报告生成器
├── data_manager.py      # 数据管理模块
├── config.py           # 配置文件
├── requirements.txt    # 依赖包列表
├── README.md          # 项目说明文档
├── data/              # 数据存储目录
│   └── trending_YYYY-MM-DD.json
├── reports/           # 报告输出目录
│   └── GitHub热门项目_YYYY-MM-DD.md
└── logs/             # 日志文件目录
    └── github_spider_YYYYMM.log
```

## ⚙️ 配置说明

主要配置项在 `config.py` 文件中：

```python
# 定时任务配置
SCHEDULE_TIME = "12:00"  # 每天执行时间

# 爬取配置
MAX_REPOSITORIES = 25    # 每次爬取的最大仓库数量
REQUEST_TIMEOUT = 30     # 请求超时时间（秒）
MAX_RETRIES = 3         # 最大重试次数

# 报告配置
REPORT_TITLE = "GitHub 今日热门项目"
INCLUDE_STATISTICS = True    # 是否包含统计信息
INCLUDE_LANGUAGE_STATS = True # 是否包含语言统计
```

## 📊 数据格式

### 原始数据格式 (JSON)

```json
{
  "date": "2024-01-01",
  "crawl_time": "2024-01-01T12:00:00",
  "total_count": 25,
  "repositories": [
    {
      "name": "owner/repo-name",
      "url": "https://github.com/owner/repo-name",
      "description": "项目描述",
      "language": "Python",
      "stars": 1000,
      "forks": 200,
      "today_stars": 50,
      "authors": ["author1", "author2"],
      "crawl_time": "2024-01-01T12:00:00"
    }
  ]
}
```

### 报告格式 (Markdown)

生成的报告包含以下部分：

- 📊 **统计信息**: 总项目数、总星数、语言分布等
- 🚀 **项目列表**: 详细的项目信息和链接
- 🔗 **相关链接**: GitHub Trending相关页面

## 🛠️ 高级用法

### 自定义配置

修改 `config.py` 文件来自定义爬虫行为：

```python
# 修改爬取数量
MAX_REPOSITORIES = 50

# 修改执行时间
SCHEDULE_TIME = "09:00"

# 自定义报告标题
REPORT_TITLE = "我的GitHub热门项目日报"
```

### 数据导出

使用数据管理器导出历史数据：

```python
from data_manager import DataManager

manager = DataManager()
# 导出指定时间范围的数据
manager.export_data('2024-01-01', '2024-01-31', 'export.json')
```

### 清理旧数据

```python
from data_manager import DataManager

manager = DataManager()
# 清理30天前的数据
manager.cleanup_old_data(keep_days=30)
```

## 📋 日志说明

日志文件保存在 `logs/` 目录下，按月份分割：

- **INFO**: 正常运行信息
- **WARNING**: 警告信息（如网络重试）
- **ERROR**: 错误信息（如爬取失败）

## 🔧 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 确认GitHub可访问
   - 考虑使用代理

2. **爬取数据为空**
   - 检查GitHub Trending页面结构是否变化
   - 查看日志文件获取详细错误信息

3. **定时任务不执行**
   - 确认系统时间正确
   - 检查程序是否正常运行
   - 查看日志文件

### 调试模式

修改 `config.py` 中的日志级别：

```python
LOG_LEVEL = "DEBUG"  # 显示详细调试信息
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [GitHub](https://github.com) - 提供优秀的开源平台
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) - 强大的HTML解析库
- [Requests](https://docs.python-requests.org/) - 简洁的HTTP库
- [Schedule](https://schedule.readthedocs.io/) - 优雅的任务调度库

---

**⭐ 如果这个项目对你有帮助，请给它一个星标！**
