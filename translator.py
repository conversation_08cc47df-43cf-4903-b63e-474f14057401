"""
AI翻译模块 - 使用DeepSeek R1 API进行项目描述的智能翻译
功能：英文项目描述翻译为中文、缓存机制、错误重试
"""

import logging
import time
from typing import Optional
from openai import OpenAI

logger = logging.getLogger(__name__)


class ProjectTranslator:
    """项目描述翻译器"""
    
    def __init__(self, api_key: str):
        """
        初始化翻译器
        
        Args:
            api_key: DeepSeek API密钥
        """
        # 使用官方DeepSeek API
        # 根据官方文档: https://api-docs.deepseek.com/zh-cn/
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        self.translation_cache = {}  # 翻译缓存，避免重复翻译
    
    def translate_description(self, description: str, project_name: str = "", language: str = "") -> str:
        """
        翻译项目描述为中文
        
        Args:
            description: 英文项目描述
            project_name: 项目名称（可选，用于提供上下文）
            language: 编程语言（可选，用于提供上下文）
            
        Returns:
            中文翻译后的描述
        """
        if not description or description.strip() == "":
            return "暂无描述"
        
        # 检查缓存
        cache_key = f"{description}_{project_name}_{language}"
        if cache_key in self.translation_cache:
            logger.debug(f"使用缓存翻译: {description[:50]}...")
            return self.translation_cache[cache_key]
        
        # 如果已经是中文，直接返回
        if self._is_chinese(description):
            logger.debug(f"检测到中文描述，直接返回: {description[:50]}...")
            return description
        
        try:
            logger.info(f"正在翻译项目描述: {description[:50]}...")
            
            # 构建翻译提示词
            prompt = self._build_translation_prompt(description, project_name, language)
            
            # 调用API - 使用DeepSeek R1模型
            response = self.client.chat.completions.create(
                model="deepseek-chat",  # 官方推荐模型
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            translated = response.choices[0].message.content.strip()
            
            # 清理翻译结果
            translated = self._clean_translation(translated)
            
            # 缓存翻译结果
            self.translation_cache[cache_key] = translated
            
            logger.info(f"翻译完成: {translated[:50]}...")
            return translated
            
        except Exception as e:
            logger.error(f"翻译失败: {e}")
            # 翻译失败时返回简化的描述
            return self._fallback_translation(description)
    
    def _build_translation_prompt(self, description: str, project_name: str, language: str) -> str:
        """
        构建翻译提示词
        
        Args:
            description: 项目描述
            project_name: 项目名称
            language: 编程语言
            
        Returns:
            翻译提示词
        """
        context_info = []
        if project_name:
            context_info.append(f"项目名称: {project_name}")
        if language:
            context_info.append(f"编程语言: {language}")
        
        context = " | ".join(context_info) if context_info else ""
        
        prompt = f"""请将以下GitHub项目描述翻译成中文，要求：

1. 翻译要通俗易懂，适合中文开发者阅读
2. 清楚说明这个项目是什么，有什么作用和功能
3. 保持技术术语的准确性，但用中文表达
4. 语言要简洁明了，避免冗长
5. 如果有专业术语，用括号标注英文原词
6. 只返回翻译结果，不要其他解释

{f"上下文信息: {context}" if context else ""}

原文: {description}

中文翻译:"""
        
        return prompt
    
    def _is_chinese(self, text: str) -> bool:
        """
        检测文本是否主要为中文
        
        Args:
            text: 待检测文本
            
        Returns:
            是否为中文
        """
        chinese_chars = 0
        total_chars = 0
        
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
                chinese_chars += 1
            if char.isalnum() or '\u4e00' <= char <= '\u9fff':
                total_chars += 1
        
        if total_chars == 0:
            return False
        
        # 如果中文字符占比超过30%，认为是中文文本
        return (chinese_chars / total_chars) > 0.3
    
    def _clean_translation(self, translation: str) -> str:
        """
        清理翻译结果
        
        Args:
            translation: 原始翻译结果
            
        Returns:
            清理后的翻译结果
        """
        # 移除可能的前缀
        prefixes_to_remove = [
            "中文翻译:",
            "翻译:",
            "翻译结果:",
            "中文:",
            "描述:",
        ]
        
        for prefix in prefixes_to_remove:
            if translation.startswith(prefix):
                translation = translation[len(prefix):].strip()
        
        # 移除多余的引号
        if translation.startswith('"') and translation.endswith('"'):
            translation = translation[1:-1]
        if translation.startswith("'") and translation.endswith("'"):
            translation = translation[1:-1]
        
        return translation.strip()
    
    def _fallback_translation(self, description: str) -> str:
        """
        翻译失败时的备用方案
        
        Args:
            description: 原始描述
            
        Returns:
            简化的中文描述
        """
        # 增强的关键词替换词典
        fallback_translations = {
            # 基础词汇
            "that": "的",
            "with": "与",
            "and": "和",
            "for": "用于",
            "to": "到",
            "in": "在",
            "on": "在",
            "at": "在",
            "by": "通过",
            "from": "从",
            "of": "的",
            "the": "",
            "a": "一个",
            "an": "一个",

            # 项目描述常用词
            "melds": "融合",
            "traditional": "传统的",
            "modern": "现代的",
            "alternative": "替代方案",
            "powered by": "由...驱动",
            "community": "社区",
            "open source": "开源",
            "self hosted": "自托管",
            "virtual": "虚拟",
            "browser": "浏览器",
            "runs": "运行",
            "docker": "Docker",
            "uses": "使用",
            "WebRTC": "WebRTC技术",

            # 技术词汇
            "vector": "矢量",
            "raster": "栅格",
            "editor": "编辑器",
            "layers": "图层",
            "tools": "工具",
            "node-based": "基于节点的",
            "non-destructive": "非破坏性",
            "procedural": "程序化",
            "workflow": "工作流",
            "machine learning": "机器学习",
            "artificial intelligence": "人工智能",
            "deep learning": "深度学习",
            "data science": "数据科学",
            "web application": "网页应用",
            "mobile app": "移动应用",
            "command line tool": "命令行工具",
            "library": "库",
            "framework": "框架",
            "API": "接口",
            "database": "数据库",
            "web scraping": "网页爬虫",
            "automation": "自动化",
            "monitoring": "监控",
            "deployment": "部署",
            "containerization": "容器化",
            "microservices": "微服务",
            "serverless": "无服务器",
            "cloud": "云",
            "DevOps": "开发运维",
            "CI/CD": "持续集成/持续部署"
        }
        
        translated = description
        for en, zh in fallback_translations.items():
            translated = translated.replace(en, zh)
        
        # 如果翻译效果不好，返回原文加说明
        if translated == description:
            return f"{description}（英文项目描述）"
        
        return translated
    
    def batch_translate(self, descriptions: list, project_names: list = None, languages: list = None) -> list:
        """
        批量翻译项目描述
        
        Args:
            descriptions: 描述列表
            project_names: 项目名称列表（可选）
            languages: 编程语言列表（可选）
            
        Returns:
            翻译结果列表
        """
        if project_names is None:
            project_names = [""] * len(descriptions)
        if languages is None:
            languages = [""] * len(descriptions)
        
        results = []
        for i, desc in enumerate(descriptions):
            try:
                project_name = project_names[i] if i < len(project_names) else ""
                language = languages[i] if i < len(languages) else ""
                
                translated = self.translate_description(desc, project_name, language)
                results.append(translated)
                
                # 添加延迟避免API限制
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"批量翻译第{i+1}项失败: {e}")
                results.append(self._fallback_translation(desc))
        
        return results
    
    def clear_cache(self):
        """清空翻译缓存"""
        self.translation_cache.clear()
        logger.info("翻译缓存已清空")
    
    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self.translation_cache)
